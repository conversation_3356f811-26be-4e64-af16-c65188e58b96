<template>
	<view>
		<view class="" style="margin: 30rpx 0;display: flex;align-items: center;justify-content: center;width: 100%">
			<view class="" style="width: 39%;display: flex;align-items: center;justify-content: center;">
				收费时段
			</view>
			<view class="" style="width: 19%;display: flex;align-items: center;justify-content: center;">
				<text style="font-size: 30rpx;">价格</text><text style="font-size: 24rpx;">(元/度)</text>
			</view>
			<view class="" style="width: 1%;display: flex;align-items: center;justify-content: center;">
				=
			</view>
			<view class="" style="width: 20%;display: flex;align-items: center;justify-content: center;">
				电费
			</view>
			<view class="" style="width: 1%;display: flex;align-items: center;justify-content: center;">
				+
			</view>
			<view class="" style="width: 19%;display: flex;align-items: center;justify-content: center;">
				服务费
			</view>
		</view>
		<view class="" style="width: 100%;display: flex;flex-direction: column;align-items: center;">
			<view class="" v-for="item in list" :style="currentTime.id==item.id?'border:1px solid #FF7E32;':''"
				style="border-radius:16rpx;height: 120rpx;width: 702rpx;background-color: #FFFFFF;display: flex;align-items: center;padding: 0 30rpx;margin-top: 20rpx;position: relative;">
				<view class="" v-if="currentTime.id==item.id" style="position: absolute;top: 0;left: 0;background: linear-gradient(90deg, #F77F0B 0%, #EC3902 102.23%);display: flex;align-items: center;justify-content: center;
				width: fit-content;padding:6rpx;border-top-left-radius: 16rpx;border-bottom-right-radius: 16rpx;color: #FFFFFF;font-size: 20rpx;">
					当前时段
				</view>
				<view class="" 
					style="min-width: 30%;display: flex;align-items: center;justify-content: center;font-size: 28rpx;">
					{{item.startTime.split(':')[0]+':'+item.startTime.split(':')[1]}}
					~
					{{item.endTime.split(':')[0]+':'+item.endTime.split(':')[1]}}
				</view>
				<view class=""
					style="min-width:23%;display: flex;align-items: center;justify-content: flex-end;font-size: 28rpx;color: #FF7E32;font-size: 32rpx;">
					{{(item.elecPrice + item.sevicePrice).toFixed(4)}}
				</view>
				<view class=""
					style="min-width:23%;display: flex;align-items: center;justify-content: flex-end;font-size: 28rpx;color: #FFAF71;font-size: 32rpx;">
					{{item.elecPrice}}
				</view>
				<view class=""
					style="min-width:23%;display: flex;align-items: center;justify-content: flex-end;font-size: 28rpx;color: #FFAF71;font-size: 32rpx;">
					{{item.sevicePrice}}
				</view>
			</view>
		</view>
		<view style="margin-top: 30rpx;margin-left: 40rpx;">
			<text style="font-size: 34rpx;color: #222222;">充电费用说明</text>
		</view>
		<view style="margin-top: 30rpx;margin-left: 40rpx;">
			<p style="font-size: 26rpx;color: #00C7A3;">·电费</p>
			<view style="font-size: 26rpx;color: #999999;margin-top: 14rpx;width: 640rpx;">
				根据电价政策、场站用电类型及充电峰平谷时段而调整，为电网收取</view>
		</view>
		<view style="margin-top: 30rpx;margin-left: 40rpx;">
			<p style="font-size: 26rpx;color: #00C7A3;">·服务费</p>
			<p style="font-size: 26rpx;color: #999999;margin-top: 14rpx">包含了站点运营费、设备维护费、人员费用等</p>
		</view>
		<view style="margin-top: 30rpx;margin-left: 40rpx;">
			<p style="font-size: 26rpx;color: #00C7A3;">·跨时段计费</p>
			<view style="font-size: 26rpx;color: #999999;margin-top: 14rpx;width: 640rpx;">跨不同时段充的电量，订单会自动分段的价格计算，无需手动操作
			</view>
		</view>
		<view class="" style="height: 80rpx;">

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				currentTime:''
			}
		},
		async onLoad(options) {
			await this.$onLaunched;
			this.$iBox.http('getEquipmentPolicy', {
				connectorId: options.id
			})({
				method: 'get'
			}).then(res => {
				this.list = res.data

				let a = this.$moment().unix()
				let today = this.$moment().format('YYYY-MM-DD')
				let currentTime = ''
				this.list.forEach(item => {
					let startTime =today+' '+ item.startTime
					let endTime =today+' ' +item.endTime
					
					let s = this.$moment(startTime, 'YYYY-MM-DD HH:mm:ss').unix()
					let e = this.$moment(endTime, 'YYYY-MM-DD HH:mm:ss').unix()
					if(a<e&&a>s){
						currentTime = item
					}

				})
				
				this.currentTime = currentTime
				console.log(currentTime);
			})



		},
		methods: {

		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #F4F6F8;
	}
</style>