<template>
	<view class="shop-detail-container">
		<!-- 店铺图片 -->
		<view class="shop-banner">
			<swiper class="banner-swiper" :autoplay="true" :interval="3000">
				<swiper-item v-for="item in shopInfo.images" :key="item">
					<image :src="item" mode="aspectFill" class="banner-image"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 店铺信息 -->
		<view class="shop-info-card">
			<view class="shop-header">
				<text class="shop-name">{{shopInfo.shopName}}</text>
				<view class="shop-status" :class="shopInfo.status == 1 ? 'available' : 'unavailable'">
					<text>{{shopInfo.status == 1 ? '营业中' : '暂停营业'}}</text>
				</view>
			</view>
			<text class="shop-address">{{shopInfo.address}}</text>
			<view class="shop-tags">
				<text class="tag" v-if="shopInfo.distance">距离{{shopInfo.distance}}km</text>
				<text class="tag price">¥{{shopInfo.price}}/次</text>
				<text class="tag">设备{{shopInfo.deviceCount}}台</text>
			</view>
		</view>

		<!-- 设备列表 -->
		<view class="device-section">
			<text class="section-title">洗车设备</text>
			<view class="device-list">
				<view class="device-item" v-for="item in deviceList" :key="item.id" @click="selectDevice(item)">
					<view class="device-info">
						<text class="device-name">{{item.deviceName}}</text>
						<text class="device-code">设备编号：{{item.deviceCode}}</text>
						<text class="device-price">价格：¥{{item.price}}/次</text>
					</view>
					<view class="device-status" :class="item.status == 1 ? 'available' : 'busy'">
						<text>{{item.status == 1 ? '可用' : '使用中'}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 洗车价格 -->
		<view class="price-section">
			<text class="section-title">洗车价格</text>
			<view class="price-list" v-if="priceInfo">
				<view class="price-item">
					<text class="price-name">冲洗</text>
					<text class="price-desc">基础清洗</text>
					<text class="price-value">¥{{priceInfo.cxPrice}}</text>
				</view>
				<view class="price-item">
					<text class="price-name">泡沫</text>
					<text class="price-desc">泡沫清洁</text>
					<text class="price-value">¥{{priceInfo.pmPrice}}</text>
				</view>
				<view class="price-item">
					<text class="price-name">洗车</text>
					<text class="price-desc">全面清洗</text>
					<text class="price-value">¥{{priceInfo.xcPrice}}</text>
				</view>
				<view class="price-item">
					<text class="price-name">吸收</text>
					<text class="price-desc">水分吸收</text>
					<text class="price-value">¥{{priceInfo.xsPrice}}</text>
				</view>
				<view class="price-item">
					<text class="price-name">暂停</text>
					<text class="price-desc">暂停计费</text>
					<text class="price-value">¥{{priceInfo.ztPrice}}</text>
				</view>
				<view class="price-item">
					<text class="price-name">最低消费</text>
					<text class="price-desc">起步价格</text>
					<text class="price-value">¥{{priceInfo.zdPrice}}</text>
				</view>
			</view>

			<!-- 价格说明 -->
			<view class="price-tips">
				<text class="tips-title">价格说明</text>
				<view class="tips-content">
					<text class="tip-item">• 按实际使用时长计费，最低消费¥{{priceInfo ? priceInfo.zdPrice : '0.00'}}</text>
					<text class="tip-item">• 暂停功能可临时停止计费，方便您处理其他事务</text>
					<text class="tip-item">• 建议使用完整洗车流程：冲洗→泡沫→洗车→吸收</text>
				</view>
			</view>
		</view>

		<!-- 服务介绍 -->
		<view class="service-section">
			<text class="section-title">服务介绍</text>
			<view class="service-content">
				<text class="service-text">{{shopInfo.description || '专业自助洗车服务，24小时营业，设备先进，价格实惠。'}}</text>
			</view>
		</view>

		<!-- 营业时间 -->
		<view class="hours-section">
			<text class="section-title">营业时间</text>
			<text class="hours-text">{{shopInfo.businessHours || '24小时营业'}}</text>
		</view>

		<!-- 联系方式 -->
		<view class="contact-section">
			<text class="section-title">联系方式</text>
			<view class="contact-item" @click="callPhone">
				<uv-icon name="phone" size="20" color="#00C7A3"></uv-icon>
				<text class="contact-text">{{shopInfo.phone || '暂无'}}</text>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<uv-button
				type="info"
				@click="showLocation"
				:custom-style="locationButtonStyle"
			>
				查看位置
			</uv-button>
			<uv-button
				type="primary"
				@click="scanCode"
				:custom-style="scanButtonStyle"
			>
				扫码洗车
			</uv-button>
		</view>

		<!-- 登录弹窗 -->
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
import {
	mapState,
	mapActions
} from 'vuex';

export default {
	data() {
		return {
			shopId: '',
			shopInfo: {
				shopName: '',
				address: '',
				images: [],
				status: 1,
				distance: '',
				price: '0.00',
				deviceCount: 0,
				description: '',
				businessHours: '',
				phone: '',
				latitude: '',
				longitude: ''
			},
			deviceList: [],
			priceInfo: null,
			hackReset: false,
			if_login: false,
			locationButtonStyle: {
				background: '#F5F5F5',
				color: '#666666',
				borderRadius: '50rpx',
				height: '88rpx',
				flex: '1',
				marginRight: '20rpx'
			},
			scanButtonStyle: {
				background: 'linear-gradient(90deg, #00C7A3 0%, #00A085 100%)',
				borderRadius: '50rpx',
				height: '88rpx',
				flex: '2'
			}
		}
	},
	computed: {
		...mapState('login', ['userInfo']),
	},
	async onLoad(options) {
		await this.$onLaunched;
		if (options.shopId) {
			this.shopId = options.shopId;
			this.loadShopDetail();
			this.loadDeviceList();
			this.loadPriceInfo();
		}
	},
	methods: {
		...mapActions('login', ['pushWash']),
		
		// 加载店铺详情
		loadShopDetail() {
			uni.showLoading({ title: '加载中...' });
			
			this.$iBox.http('getSelfWashShopDetail', { shopId: this.shopId })({
				method: 'get'
			}).then(res => {
				this.shopInfo = res.data;
				uni.hideLoading();
			}).catch(err => {
				uni.hideLoading();
				console.log('加载店铺详情失败', err);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			});
		},
		
		// 加载设备列表
		loadDeviceList() {
			this.$iBox.http('getSelfWashDeviceList', { shopId: this.shopId })({
				method: 'get'
			}).then(res => {
				this.deviceList = res.data;
			}).catch(err => {
				console.log('加载设备列表失败', err);
			});
		},

		// 加载价格信息
		loadPriceInfo() {
			this.$iBox.http('onceWashCard/getCarWashSelfPriceList', { shopId: this.shopId })({
				method: 'get'
			}).then(res => {
				if (res.data && res.data.length > 0) {
					this.priceInfo = res.data[0];
					// 处理价格数据，确保都是数字格式
					this.formatPriceData();
				}
			}).catch(err => {
				console.log('加载价格信息失败', err);
			});
		},

		// 格式化价格数据
		formatPriceData() {
			if (this.priceInfo) {
				// 确保价格都是数字格式，便于显示
				this.priceInfo.cxPrice = parseFloat(this.priceInfo.cxPrice || 0).toFixed(2);
				this.priceInfo.pmPrice = parseFloat(this.priceInfo.pmPrice || 0).toFixed(2);
				this.priceInfo.xcPrice = parseFloat(this.priceInfo.xcPrice || 0).toFixed(2);
				this.priceInfo.xsPrice = parseFloat(this.priceInfo.xsPrice || 0).toFixed(2);
				this.priceInfo.ztPrice = parseFloat(this.priceInfo.ztPrice || 0).toFixed(2);
				this.priceInfo.zdPrice = parseFloat(this.priceInfo.zdPrice || 0).toFixed(2);
			}
		},
		
		// 选择设备
		selectDevice(device) {
			if (device.status != 1) {
				uni.showToast({
					title: '设备使用中',
					icon: 'none'
				});
				return;
			}
			
			uni.navigateTo({
				url: `/pages/selfWash/billing/billing?deviceId=${device.id}`
			});
		},
		
		// 扫码洗车
		scanCode() {
			if (!this.checkLogin()) return;

			uni.scanCode({
				onlyFromCamera: true,
				success: (res) => {
					console.log('扫码结果：', res);
					this.handleScanResult(res);
				},
				fail: (err) => {
					console.log('扫码失败', err);
					uni.showToast({
						title: '扫码失败',
						icon: 'none'
					});
				}
			});
		},
		
		// 处理扫码结果
		handleScanResult(scanResult) {
			try {
				let deviceId = '';
				if (scanResult.result.includes('deviceId=')) {
					deviceId = scanResult.result.split('deviceId=')[1].split('&')[0];
				}
				
				if (deviceId) {
					uni.navigateTo({
						url: `/pages/selfWash/billing/billing?deviceId=${deviceId}`
					});
				} else {
					uni.showToast({
						title: '无效的二维码',
						icon: 'none'
					});
				}
			} catch (error) {
				console.log('解析二维码失败', error);
				uni.showToast({
					title: '二维码格式错误',
					icon: 'none'
				});
			}
		},
		
		// 查看位置
		showLocation() {
			if (!this.shopInfo.latitude || !this.shopInfo.longitude) {
				uni.showToast({
					title: '位置信息不完整',
					icon: 'none'
				});
				return;
			}
			
			wx.openLocation({
				latitude: parseFloat(this.shopInfo.latitude),
				longitude: parseFloat(this.shopInfo.longitude),
				name: this.shopInfo.shopName,
				address: this.shopInfo.address
			});
		},
		
		// 拨打电话
		callPhone() {
			if (!this.shopInfo.phone) {
				uni.showToast({
					title: '暂无联系电话',
					icon: 'none'
				});
				return;
			}

			uni.makePhoneCall({
				phoneNumber: this.shopInfo.phone
			});
		},

		// 统一的登录检查方法
		checkLogin() {
			if (!this.userInfo.phone) {
				this.hackReset = true;
				this.if_login = true;
				return false;
			}
			return true;
		},

		// 登录成功回调
		loginSucess() {
			this.hackReset = false;
			this.if_login = false;
		}
	}
}
</script>

<style lang="scss">
page {
	background: #F4F6F8;
}

.shop-detail-container {
	min-height: 100vh;
	padding-bottom: 120rpx;
	
	.shop-banner {
		height: 400rpx;
		
		.banner-swiper {
			height: 100%;
			
			.banner-image {
				width: 100%;
				height: 100%;
			}
		}
	}
	
	.shop-info-card {
		background: #FFFFFF;
		padding: 30rpx;
		margin: 24rpx;
		border-radius: 16rpx;
		
		.shop-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			
			.shop-name {
				font-size: 36rpx;
				font-weight: 600;
				color: #222222;
			}
			
			.shop-status {
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				font-size: 22rpx;
				
				&.available {
					background: #E8F5E8;
					color: #00C7A3;
				}
				
				&.unavailable {
					background: #FFF0F0;
					color: #FF6B6B;
				}
			}
		}
		
		.shop-address {
			font-size: 26rpx;
			color: #666666;
			margin-bottom: 20rpx;
		}
		
		.shop-tags {
			display: flex;
			
			.tag {
				padding: 6rpx 12rpx;
				background: #F5F5F5;
				color: #666666;
				font-size: 22rpx;
				border-radius: 6rpx;
				margin-right: 12rpx;
				
				&.price {
					background: #FFF6ED;
					color: #FF7E32;
				}
			}
		}
	}
	
	.device-section, .price-section, .service-section, .hours-section, .contact-section {
		background: #FFFFFF;
		margin: 24rpx;
		border-radius: 16rpx;
		padding: 30rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #222222;
			margin-bottom: 24rpx;
		}
	}
	
	.device-list {
		.device-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx;
			border: 2rpx solid #EFEFEF;
			border-radius: 12rpx;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.device-info {
				flex: 1;
				
				.device-name {
					display: block;
					font-size: 28rpx;
					font-weight: 600;
					color: #222222;
					margin-bottom: 8rpx;
				}
				
				.device-code, .device-price {
					display: block;
					font-size: 24rpx;
					color: #666666;
					margin-bottom: 6rpx;
				}
			}
			
			.device-status {
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				font-size: 22rpx;
				
				&.available {
					background: #E8F5E8;
					color: #00C7A3;
				}
				
				&.busy {
					background: #FFF0F0;
					color: #FF6B6B;
				}
			}
		}
	}

	.price-list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
		margin-bottom: 30rpx;

		.price-item {
			background: #F8F9FA;
			border-radius: 12rpx;
			padding: 24rpx;
			text-align: center;
			min-height: 140rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.price-name {
				display: block;
				font-size: 28rpx;
				font-weight: 600;
				color: #333333;
				margin-bottom: 6rpx;
				line-height: 1.2;
			}

			.price-desc {
				display: block;
				font-size: 22rpx;
				color: #999999;
				margin-bottom: 12rpx;
				line-height: 1.2;
			}

			.price-value {
				font-size: 32rpx;
				font-weight: 600;
				color: #00C7A3;
			}
		}
	}

	.price-tips {
		background: #FFF9E6;
		border-radius: 12rpx;
		padding: 24rpx;
		border-left: 4rpx solid #FFB800;

		.tips-title {
			font-size: 28rpx;
			font-weight: 600;
			color: #333333;
			margin-bottom: 16rpx;
			display: block;
		}

		.tips-content {
			.tip-item {
				display: block;
				font-size: 24rpx;
				color: #666666;
				line-height: 1.6;
				margin-bottom: 8rpx;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}

	.service-content {
		.service-text {
			font-size: 26rpx;
			color: #666666;
			line-height: 1.6;
		}
	}
	
	.hours-text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.contact-item {
		display: flex;
		align-items: center;
		
		.contact-text {
			font-size: 26rpx;
			color: #666666;
			margin-left: 12rpx;
		}
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #FFFFFF;
		padding: 24rpx;
		display: flex;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
}
</style>
