
@mixin flex($direction: row) {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: $direction;
}
.uv-block{
	padding: 14px;
	&__section{
		margin-bottom:10px;
	}
	&__title {
		margin-top:10px;
		font-size: 15px;
		@if variable-exists(uv-content-color) {
			color: $uv-content-color;
		}@else {
			color: #606266;
		}
		margin-bottom:10px;
	}
	&__flex{
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
	}
}

// 使用了cell组件的icon图片样式
.uv-cell-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 8rpx;
}

.uv-page {
	padding: 15px 15px 20px 15px;
}

.uv-demo-block {
	// #ifndef APP-NVUE
	flex: 1;
	// #endif
	margin-bottom: 23px;
	
	&__content {
		@include flex(column);
	}
	
	&__title {
		font-size: 14px;
		color: rgb(143, 156, 162);
		margin-bottom: 8px;
		@include flex;
	}
}

