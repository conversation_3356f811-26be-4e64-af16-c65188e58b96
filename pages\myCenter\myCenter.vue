<template>
	<view>
		<view class="search_box" style="">
			<view class="title" style="padding: 0 32rpx;"
				:style="{marginTop:searchBarTop + 'px',height:searchBarHeight + 'px'}">
				个人中心
			</view>
		</view>
		<view class="" :style="{height:searchBarHeight+searchBarTop + 'px'}">

		</view>
<!-- 		<view class="" style="position: fixed;top: -40rpx;right: -40rpx;z-index: 8;">
			<image src="https://www.kemanfang.net/xcx_resource/xcbj.png" style="height: 303rpx;width: 305rpx;" mode="">
			</image>
		</view> -->
		<view class="box">

			<!--  -->
			<view class="nameBox" style="position: relative;" >
				<view class="" style="display: flex;align-items: center;">
					<image :src="userInfo.avatarUrl?userInfo.avatarUrl:'/static/tabs/mine_active.png'"
						style="width: 140rpx;height: 140rpx;border-radius: 50%;border: 4.17px solid #FFFFFF" mode="">
					</image>
					<view class="" style="display: flex;flex-direction: column;">
						<text
							style="font-size: 30rpx;padding-left: 20rpx;">{{centerUser.nickname?centerUser.nickname:'请授权昵称'}}</text>
						<text
							style="font-size: 30rpx;padding-left: 20rpx;">{{centerUser.phone?centerUser.phone:'请编辑资料'}}</text>

					</view>

				</view>
				<view class="" @click="editInfo"
					style="position: absolute;right: 0;margin: 0 auto;height: 55rpx;width: 160rpx;z-index: 999;font-size: 24rpx;
				border-top-left-radius: 66rpx;border-bottom-left-radius: 66rpx;display: flex;align-items: center;justify-content: center;color: #FFFFFF;background-color: #00C7A3;">
					编辑资料
				</view>
			</view>
			<view class="gridBox">
				<view class="" @click="recharge">
					<view class="item" style="display: flex;align-items: center;">
						<image src="/static/images/qb.png" style="height: 45rpx;width: 47rpx;" mode=""></image>
						<view class="" style="display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;margin-left: 36rpx;">
							<text class="item_title">{{centerUser.userBalance.balance}}元</text>
							<text class="item_title1">账户余额(元)</text>
						</view>
					</view>

				</view>
				<view class="item" @click="myCoupon">
					<image src="/static/images/yhq.png" style="height: 45rpx;width: 47rpx;" mode=""></image>
					<view class="" style="display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;margin-left: 36rpx;">
						<text class="item_title">{{couponList.length}}张</text>
						<text class="item_title1">我的优惠券</text>
					</view>

				</view>
			</view>
		</view>


		<view class="elseBox">
			<view class="" style="padding:30rpx;font-size: 36rpx;font-weight: 600;color: #222222;">
				我的服务
			</view>
			<view class="" style="display: flex;align-items: center;height: 103rpx;">
				<view class="" style="width: 25%;height: 100%;display: flex;align-items: center;justify-content: center;">
						<view class=""
							style="box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;flex-direction: column;"
							@click="toCouponCenter">
							<image src="/static/images/qbdd.png" style="height: 50rpx;width: 50rpx;" mode=""></image>
							<text style="font-size: 22rpx;color: #666666;">领券中心</text>
						
						</view>
				</view>
				<view class="" style="width: 25%;height: 100%;display: flex;align-items: center;justify-content: center;">
						<view class=""
							style="box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;flex-direction: column;"
							@click="addCar">
							<image src="/static/images/cars.png" style="height: 50rpx;width: 50rpx;" mode=""></image>
							<text style="font-size: 22rpx;color: #666666;">我的车辆</text>
						
						</view>
				</view>
				<view class="" style="width: 25%;height: 100%;display: flex;align-items: center;justify-content: center;">
						<view class=""
							style="box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;flex-direction: column;"
							@click="fapiao">
							<image src="/static/images/fp.png" style="height: 50rpx;width: 50rpx;" mode=""></image>
							<text style="font-size: 22rpx;color: #666666;">发票中心</text>
						
						</view>
				</view>
				<view class="" style="width: 25%;height: 100%;display: flex;align-items: center;justify-content: center;">
						<view class=""
							style="box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;flex-direction: column;"
							@click="taitou">
							<image src="/static/images/fp.png" style="height: 50rpx;width: 50rpx;" mode=""></image>
							<text style="font-size: 22rpx;color: #666666;">抬头管理</text>
						
						</view>
				</view>
				<view class="" style="width: 25%;height: 100%;display: flex;align-items: center;justify-content: center;">
					<view class=""
						style="box-sizing: border-box;display: flex;align-items: center;justify-content: space-between;flex-direction: column;"
						@click="kefu">
						<uv-icon name="weixin-fill" size="30" color="#00aa00"></uv-icon>
						<text style="font-size: 22rpx;color: #666666;">微信客服</text>

					</view>
				</view>
			</view>

		</view>
		<uv-popup ref="show" mode="bottom" round="14">
			<view class=""
				style="position: relative;width: 100%;height: 300rpx;display: flex;align-items: center;justify-content: center;">
				<view class=""
					style="display: flex;flex-direction: column;align-items:center;justify-content: space-between;height: 184rpx;">
					<image src="https://www.kemanfang.net/xcx_resource/customer.png"
						style="width: 128rpx;height: 128rpx;padding-right: 10rpx;" mode=""></image>
					<text style="font-size: 28rpx;color: #666666;">点击咨询客服</text>
				</view>
				<button class="kf_button flex align-center justify-center" open-type="contact" style="position: absolute;top:0;right: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0);display: flex;
			align-items: center;justify-content: flex-end;">
				</button>
			</view>
		</uv-popup>
		<m-tabbar></m-tabbar>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				centerUser: null,
				couponList: [],
				carNumberList: [],
				searchBarTop: '',
				searchBarHeight: '',
			}
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		onReady() {
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
		},
		async onShow() {
			await this.$onLaunched;
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			this.$iBox.http('getUserInfo', {

			})({
				method: 'get'
			}).then(res => {
				let centerUser = res.data
				centerUser.token = this.userInfo.token
				centerUser.session_key = this.userInfo.session_key
				this.centerUser = centerUser
				this.updateUserInfo(this.centerUser)
				this.$iBox.http('getUserCarWashCouponList', {
					pageNumber: 1,
					pageSize: 1000
				})({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.couponList = res.data.list
				})

				this.$iBox.http('getUserCar', {

				})({
					method: 'get'
				}).then(res => {
					if (res.data.length > 0) {
						this.carNumberList = res.data
					}
				})

			}).catch(function(error) {
				console.log('网络错误', error)
			})
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			myCoupon() {
				uni.navigateTo({
					url: '/pages/myCoupons/myCoupons'
				})
			},
			toCouponCenter(){
				uni.navigateTo({
					url: '/pages/myCoupons/couponCenter'
				})
			},
			takePhone() {
				wx.makePhoneCall({
					phoneNumber: '************' //仅为示例，并非真实的电话号码
				});
			},
			addCar() {
				uni.navigateTo({
					url: '/pages/addCar/addCar'
				})
			},
			editInfo() {
				console.log('kl');
				uni.navigateTo({
					url: '/pages/myCenter/supplementInfo'
				})
			},
			taitou(){
				uni.navigateTo({
					url:'/pages/fapiao/titleCenter'
				})
			},
			couponCenter() {
				uni.navigateTo({
					url: '/pages/couponCenter/couponCenter'
				})
			},
			fapiao(){
				uni.navigateTo({
					url: '/pages/fapiao/fapiao'
				})
			},
			recharge() {
				uni.navigateTo({
					url: '/pages/recharge/recharge?type=1'
				})
			},
			washOrder() {
				uni.navigateTo({
					url: '/pages/washOrder/washOrder'
				})
			},
			toOrder() {
				uni.switchTab({
					url: '/pages/order/order'
				})
			},
			powerOeder() {
				uni.navigateTo({
					url: '/pages/powerOeder/powerOeder'
				})
			},
			kefu() {
				this.$refs.show.open()
			}

		},
		onShareAppMessage() {
			
		}
	}
</script>

<style>
	page {
		background: linear-gradient(180deg, #E2FFF9 -8.71%, #FFFFFF 103.17%);

	}

	view {
		box-sizing: border-box;
	}
</style>

<style scoped lang="scss">
	.search_box {
		// height: 21vh;
		// background-color: #FFFFFF;
		position: fixed;
		top: 0;
		z-index: 9999;
		display: flex;
		flex-direction: column;
		// justify-content: center;
		align-items: center;
		// height: 460rpx;
		width: 100%;


		.title {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			.address {
				position: absolute;
				left: 0;
				width: fit-content;
				height: 100%;
				display: flex;
				align-items: center;
				color: #000000;
			}
		}
	}

	.box {

		border-bottom-left-radius: 30rpx;
		border-bottom-right-radius: 30rpx;
		padding: 40rpx 0;
		height: 400rpx;
		position: relative;
		z-index: 1;

		.nameBox {
			display: flex;
			align-items: center;
			padding: 30rpx;
			height: 100rpx;
			justify-content: space-between;
		}

		.gridBox {

			display: flex;
			align-items: center;
			height: 166rpx;
			margin-top: 80rpx;
			padding: 30rpx;
			justify-content: space-between;

			.item {
				height: 166rpx;
				width: 334rpx;
				display: flex;

				align-items: center;
				padding-left: 40rpx;
				border-radius: 16rpx;
				background-color: #FFFFFF;

				.item_title {
					font-size: 43rpx;
					font-weight: 600;
				}

				.item_title1 {
					font-size: 30rpx;
					color: #606266;
				}
			}
		}
	}

	.billBox {
		margin: 0 auto;
		margin-top: -100rpx;
		width: 650rpx;
		height: 240rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		padding: 30rpx;
		z-index: 2;
		position: relative;

		.titleBox {
			color: #333333;
			font-size: 38rpx;
			font-weight: 600;
			height: 20%;

		}

		.contentBox {
			margin-top: 30rpx;
			height: 80%;
			display: flex;

			.itemBox {
				display: flex;
				flex-direction: column;
				align-items: center;
				color: #333333;
				font-size: 28rpx;
				width: 33%;
				height: 70%;
				justify-content: space-around;
			}
		}
	}

	.elseBox {
		width: 710rpx;
		height: 246rpx;
		margin: 30rpx auto;
		border-radius: 16rpx;
		background-color: #FFFFFF;


	}
</style>