<template>
	<view>
		<view class="" style="padding: 30rpx;font-size: 34rpx;color: #222222;">
			充值金额
		</view>
		<view class=""
			style="padding:48rpx;width: 100%;display: flex;flex-wrap: wrap;background-color: #FFFFFF;margin: 0 auto;border-radius: 16rpx;">
			<view class="" style="width: 33%;height: 110rpx;margin-bottom: 20rpx;" v-for="item in rechargeRule">
				<view class="" @click="choose(item)"
					style="width: 200rpx;height: 110rpx;border-radius: 16rpx;display: flex;flex-direction: column;align-items: center;background-color: #EFEFEF;justify-content: center;color: #999999;justify-content: center;"
					:style="id.id==item.id?'background:#FFEAD4;border:1px solid #FF7E32;color:#FF7E32;':''">
					<text style="font-size: 32rpx;">{{item.rechargeAmount}}元</text>
					<text style="font-size: 24rpx;color: #999999;">{{item.description}}</text>
				</view>
			</view>
			<view class=""
				style="margin-top: 40rpx;width: 644rpx;height: 84rpx;background-color: #EFEFEF;display: flex;align-items: center;padding: 0 30rpx;justify-content: space-between;">
				<text style="font-size: 26rpx;font-weight: 600;color: #222222;margin-right: 10rpx;">其他金额</text>
				<input type="number" style="width: 340rpx;" placeholder="请输入其他金额" v-model="preMoney"/>
				<text style="color: #FF7E32;">元</text>
			</view>

		</view>
		<view class=""
			style="width: 94%;height: 104rpx;margin: 30rpx auto;border-radius: 16rpx;background-color: #FFFFFF;display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;">
			<view class="" style="display: flex;align-items: center;">
				<image src="/static/images/wxpay.png" style="height: 40rpx;width: 40rpx;" mode=""></image>
				<text style="margin-left: 6rpx;">微信支付</text>
			</view>
			<view class=""
				style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;background:#00C7A3;">

				<uv-icon name="checkmark" color="#ffffff" size="16"></uv-icon>
			</view>
		</view>
		<view class=""
			style="position: fixed;bottom: 40rpx;width: 100%;display: flex;align-items: center;justify-content: center;height: 88rpx;">
			<view class=""
				style="width: 680rpx;height: 88rpx;border-radius: 88rpx;background-color: #FF7E32;color: #FFFFFF;display: flex;align-items: center;justify-content: center;"
				@click="start">
				立即充值
			</view>
		</view>
		<!-- <uv-keyboard ref="keyboard" mode="number" @change="change" @backspace="backspace"></uv-keyboard> -->
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
	import { duration } from 'moment/moment';
	import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				rechargeRule: [],
				id: 0,
				type: '',
				preMoney:'',
				hackReset: true,
				if_login:false,
			};
		},
		computed: {
			...mapState('login', ['userInfo', 'car','pNum'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			this.myamount = this.userInfo.amount;
			this.type = options.type
			this.$iBox
				.http('getUserChargeSetting', {
					pageNumber: 1,
					pageSize: 10
				})({
					method: 'post'
				})
				.then(res => {
					this.rechargeRule = res.data.list;
					this.id = res.data.list[0];
				})
				.catch(function(error) {
					console.log('网络错误', error);
				});
				uni.hideKeyboard()
				
		},
		methods: {
			...mapActions('login', ['toLogin', 'updateUserInfo', 'pushCode']),
			choose(e) {
				this.id = e;
				console.log(this.id);
			},
			loginSucess() {
			
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false
			
					} else {
						this.if_login = true
					}
				})
			},
			change(val){
				this.preMoney += val;
				console.log(this.preMoney);
			},
			backspace(){
				if (this.preMoney.length) this.preMoney = this.preMoney.substr(0, this.preMoney.length - 1);
			},
			toInput(){
				uni.hideKeyboard()
				this.$refs.keyboard.open();
			},
			start() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false
						throttle(()=>{
							if (this.type == 2) {
								let params = {
									payType: 2,
									carNumber: this.car,
									connectorId: this.pNum
								}
								
								if(this.preMoney){
									params.preMoney = this.preMoney
								}else{
									params.settingId=this.id.id
								}
								
								this.$iBox.http('startCharge', params)({
									method: 'post'
								}).then(res => {
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.sign,
										success: (res) => {
											console.log(res,'res');
											uni.navigateTo({
												url: '/pages/resultPage/resultPage'
											})
							
										},
										fail: function(err) {
											console.log(err,'err');
											uni.hideLoading()
										}
									});
								}).catch(err=>{
									uni.showToast({
										icon:'error',
										title:err.message,
										duration:2000
									})
								})
							} else {
								let params = {}
								if(this.preMoney){
									params.amount = this.preMoney
								}else{
									params.settingId=this.id.id
								}
								
								this.$iBox.http('userRecharges', params)({
									method: 'post'
								}).then(res => { 
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.sign,
										success: (res) => {
											console.log(res,'res');
											uni.navigateBack()
									
										},
										fail: function(err) {
											console.log(err,'err');
											uni.hideLoading()
										}
									});
								})
								
								
								console.log(this.id);
							}
						},2000,true)
					} else {
						this.if_login = true
					}
				})
				
				

			}

		}
	};
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>