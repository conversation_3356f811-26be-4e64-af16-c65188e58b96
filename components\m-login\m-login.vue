<template>
	<view>
		<uv-popup ref="popupLogin" mode="bottom" round="20">
			<view class="loginBox">
				<p style="font-size: 36rpx;font-weight: 600;">授权手机号</p>
				<p style="font-size: 30rpx;color: #909399;">位了更方便联系您充电与停车通知，请授权手机号</p>
				<view class="item2">
					<button type="primary" open-type="getPhoneNumber|agreePrivacyAuthorization"
						@getphonenumber="getPhoneNumber"
						style="background: #4e4eff;border-radius: 36rpx;width: 600rpx;font-size: 32rpx;"><text
							class="cuIcon-weixin text-white padding-right"></text>授权手机号</button>
					<button type="default"
						style="margin-top: 16rpx;color: #2b2b2b;border-radius: 36rpx;background-color: #e6e6ed;width: 600rpx;font-size: 32rpx;"
						@click="closeLogin">拒绝</button>
				</view>
			</view>
		</uv-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-login",
		data() {
			return {
				phone: ''
			};
		},
		props: {
			customStyle: {
				type: Object,
				default: null
			}
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		mounted() {
			this.$refs.popupLogin.open();
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			closeLogin() {
				this.$refs.popupLogin.close();
				this.$emit('closeToLogin', 'success')
			},

			// 获取手机号
			getPhoneNumber(e) {
				let that = this;
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					console.log('phone');
					this.$iBox.http('getUserPhone', {
						iv: e.detail.iv,
						encData: e.detail.encryptedData

					})({
						method: 'post'
					}).then(res2 => {
						// 注册会员

						let params = {
							phone: res2.data
						}
						this.$iBox.http('updateUserInfo', params)({
							method: 'post'
						}).then(resUp => {
							// 更新用户信息
							this.$iBox.http('getUserInfo', {
								
							})({
								method: 'get'
							}).then(res1 => {
								let userInfo = res1.data
								userInfo.session_key = this.userInfo.session_key
								userInfo.token = this.userInfo.token
								this.updateUserInfo(userInfo)
								uni.showToast({
									title: '注册成功！'
								})
								this.$emit('loginTo', 'success')
								this.showLogin = false
							})
						}).catch(function(error) {
							console.log('网络错误', error)
						})

					})
				} else {
					uni.showModal({
						title: '提示',
						content: e.detail.errMsg + ';请重新点击授权！'
					})
				}
			},
		},
	}
</script>

<style scoped lang="scss">
	.loginBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		height: 400rpx;
		padding: 30rpx;
		position: relative;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;

		.item2 {
			height: 360rpx;
			width: 600rpx;
			margin: 0 auto;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}
</style>