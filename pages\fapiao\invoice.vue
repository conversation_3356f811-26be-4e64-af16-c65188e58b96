<template>
	<view>
		<view class="" style="display: flex;align-items: center;margin: 30rpx;position: relative;justify-content: flex-end;">
			<view class="" @click="addTitle" style="height: 60rpx;width: fit-content;border-radius: 8rpx;background-color: #00C7A3;display: flex;align-items: center;justify-content: center;padding: 14rpx;">
				<text style="color: #FFFFFF;font-size: 26rpx;">发票抬头</text>
			</view>
		</view>
		<view class="" style="width: 700rpx;margin: 30rpx auto;background-color: #FFFFFF;display: flex;align-items: center;border-radius: 16rpx;"
		 :style="id==item.id?'border: 2px solid #00C7A3;':''"
		 @click="chooseInvoice(item)" v-for="item in invoiceList">
			<view class="" style="width: 80rpx;display: flex;align-items: center;justify-content: center;">
				<view class="" :style="id==item.id?'border: 1px solid #00C7A3;':'border: 1px solid #ccced3;'" style="width: 50rpx;height: 50rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;">
					
					<uv-icon v-if="id==item.id" name="checkbox-mark" size="18" color="#00C7A3"></uv-icon>
				</view>
			</view>
			<view class="" style="width: 620rpx;padding: 30rpx;">
				<view class="" style="height: 80rpx;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px solid #e4e7ed;">
					<view class="" style="display: flex;align-items: center;color: #222222;font-size: 28rpx;">
						<text>普通发票-{{item.applyInvoiceType==1?'企业':'个人'}}</text>
					</view>
					<uv-icon name="edit-pen" size="18" color="#999999" @click.stop="edit(item)"></uv-icon>
				</view>
				<view class="" style="height: 80rpx;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px solid #e4e7ed;">
					<view class="" style="display: flex;align-items: center;color: #222222;font-size: 28rpx;">
						<text>抬头</text>
					</view>
					<text>{{item.name}}</text>
				</view>
				<view class="" style="height: 80rpx;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px solid #e4e7ed;" v-if="item.invoiceType==1">
					<view class="" style="display: flex;align-items: center;color: #222222;font-size: 28rpx;">
						<text>税号</text>
					</view>
					<text>{{item.companyNumber}}</text>
				</view>
				<view class="" style="height: 80rpx;display: flex;align-items: center;justify-content: space-between;border-bottom: 1px solid #e4e7ed;">
					<view class="" style="display: flex;align-items: center;color: #222222;font-size: 28rpx;">
						<text>邮箱</text>
					</view>
					<text>{{item.email}}</text>
				</view>
			</view>
			
		</view>
		
		<view class="" style="height: 148rpx;width: 100%;display: flex;align-items: center;justify-content: center;position: fixed;bottom: 0;">
			<view class="" @click="sure" style="height: 88rpx;width: 680rpx;border-radius: 87rpx;background-color: #00C7A3;color: #FFFFFF;display: flex;align-items: center;justify-content: center;">
				确认开票
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				invoiceList:[],
				id:'',
				invoice:null,
				orderId:'',
				type:'',
			}
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options);
			this.orderId = JSON.parse(options.id)
			this.type = options.type
			
		},
		async onShow() {
			await this.$onLaunched;
			this.$iBox.http('getUserInvoiceList', {})({
				method: 'get'
			}).then(res => {
				this.invoiceList = res.data
			})
		},
		methods: {
			chooseInvoice(e){
				this.invoice = e
				this.id = e.id
			},
			addTitle(){
				uni.navigateTo({
					url:'/pages/fapiao/addTitle'
				})
			},
			edit(e){
				uni.navigateTo({
					url:'/pages/fapiao/invoiceTitle?detail='+ JSON.stringify(e)
				})
			},
			sure(){
				
				if (!this.id) {
					uni.showToast({
						icon: 'none',
						title: '请选择一个抬头'
					})
					return
				}
				
				this.$iBox.http('saveUserInvoiceApply', {
					 orderIds: this.orderId,
					  type: this.type,
					  invoiceType: this.invoice.invoiceType,
					  invoiceId: this.invoice.id
				})({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title:'提示',
						content:'申请发票成功请注意短信或者邮箱查收',
						showCancel:false,
						success:res=>{
							uni.navigateBack()
						}
					})
				})
			}
		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #F4F6F8;
	}
</style>
