<template>
	<view class="orders-container">
		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view class="tab-item" 
				v-for="(item, index) in tabList" 
				:key="item.id"
				@click="changeTab(index)"
				:class="currentTab == index ? 'active' : ''"
			>
				{{item.name}}
			</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view scroll-y="true" class="order-list" @scrolltolower="loadMore">
			<view class="order-item" v-for="item in orderList" :key="item.id" @click="toOrderDetail(item)">
				<view class="order-header">
					<view class="order-info">
						<text class="order-title">自助洗车订单</text>
						<text class="order-no">{{item.orderNo}}</text>
					</view>
					<view class="order-status">
						<text>{{getStatusText(item.status)}}</text>
					</view>
				</view>
				
				<view class="order-content">
					<view class="shop-info">
						<view class="shop-image">
							<image :src="item.shopImage || '/static/images/default-shop.png'" mode="aspectFill"></image>
						</view>
						<view class="shop-details">
							<text class="shop-name">{{item.shopName}}</text>
							<text class="device-name">设备：{{item.deviceName}}</text>
							<text class="wash-time">洗车时间：{{item.washTime}}分钟</text>
						</view>
					</view>
					
					<view class="order-details">
						<view class="detail-row">
							<text class="detail-label">开始时间：</text>
							<text class="detail-value">{{item.startTime}}</text>
						</view>
						<view class="detail-row" v-if="item.endTime">
							<text class="detail-label">结束时间：</text>
							<text class="detail-value">{{item.endTime}}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">支付方式：</text>
							<text class="detail-value">{{getPayTypeText(item.payType)}}</text>
						</view>
					</view>
					
					<view class="cost-info">
						<view class="cost-row">
							<text class="cost-label">基础费用：</text>
							<text class="cost-value">¥{{item.baseCost}}</text>
						</view>
						<view class="cost-row" v-if="item.overtimeCost > 0">
							<text class="cost-label">超时费用：</text>
							<text class="cost-value">¥{{item.overtimeCost}}</text>
						</view>
						<view class="cost-row" v-if="item.discountAmount > 0">
							<text class="cost-label">优惠金额：</text>
							<text class="cost-value discount">-¥{{item.discountAmount}}</text>
						</view>
						<view class="cost-total">
							<text class="total-label">实付金额：</text>
							<text class="total-value">¥{{item.totalAmount}}</text>
						</view>
					</view>
				</view>
				
				<view class="order-actions">
					<view class="action-left">
						<text class="create-time">{{item.createTime}}</text>
					</view>
					<view class="action-right">
						<uv-button 
							v-if="item.status == 1" 
							size="mini" 
							type="primary"
							@click.stop="continueWash(item)"
						>
							继续洗车
						</uv-button>
						<uv-button 
							v-if="item.status == 2" 
							size="mini" 
							plain
							@click.stop="reorder(item)"
						>
							再次下单
						</uv-button>
						<uv-button 
							v-if="item.status == 2 && !item.isEvaluated" 
							size="mini" 
							type="warning"
							@click.stop="evaluate(item)"
						>
							评价
						</uv-button>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<uv-loading-icon :show="loading" mode="circle" size="20"></uv-loading-icon>
				<text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
			</view>
			
			<!-- 没有更多数据 -->
			<view class="no-more" v-if="!hasMore && orderList.length > 0">
				<text>没有更多数据了</text>
			</view>
		</scroll-view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="orderList.length == 0 && !loading">
			<uv-empty mode="list" icon="/static/images/noData.png" text="暂无订单记录"></uv-empty>
		</view>

		<!-- 评价弹窗 -->
		<uv-popup v-model="showEvaluate" mode="center" border-radius="16">
			<view class="evaluate-popup">
				<text class="evaluate-title">服务评价</text>
				<view class="evaluate-content">
					<view class="rate-section">
						<text class="rate-label">服务评分</text>
						<uv-rate v-model="evaluateData.rating" :count="5" active-color="#FFD700"></uv-rate>
					</view>
					<view class="comment-section">
						<text class="comment-label">评价内容</text>
						<uv-textarea 
							v-model="evaluateData.comment" 
							placeholder="请输入您的评价..."
							:maxlength="200"
							:show-confirm-bar="false"
						></uv-textarea>
					</view>
				</view>
				<view class="evaluate-actions">
					<uv-button type="info" @click="showEvaluate = false">取消</uv-button>
					<uv-button type="primary" @click="submitEvaluate">提交</uv-button>
				</view>
			</view>
		</uv-popup>

	<!-- 登录弹窗 -->
	<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
import {
	mapState,
	mapActions
} from 'vuex';

export default {
	data() {
		return {
			tabList: [
				{ id: 0, name: '全部' },
				{ id: 1, name: '进行中' },
				{ id: 2, name: '已完成' },
				{ id: 3, name: '已取消' }
			],
			currentTab: 0,
			orderList: [],
			loading: false,
			hasMore: true,
			params: {
				pageNumber: 1,
				pageSize: 10,
				status: null
			},
			showEvaluate: false,
			evaluateData: {
				orderId: '',
				rating: 5,
				comment: ''
			},
			hackReset: false,
			if_login: false
		}
	},
	computed: {
		...mapState('login', ['userInfo']),
	},
	async onShow() {
		await this.$onLaunched;
		if (!this.checkLogin()) return;

		this.loadOrderList();
	},
	methods: {
		...mapActions('login', ['pushOrderDetail']),
		
		// 切换标签
		changeTab(index) {
			this.currentTab = index;
			this.params.pageNumber = 1;
			this.hasMore = true;
			
			// 设置状态筛选
			if (index == 0) {
				this.params.status = null;
			} else if (index == 1) {
				this.params.status = 1; // 进行中
			} else if (index == 2) {
				this.params.status = 2; // 已完成
			} else if (index == 3) {
				this.params.status = 3; // 已取消
			}
			
			this.loadOrderList();
		},
		
		// 加载订单列表
		loadOrderList() {
			if (this.loading) return;
			
			this.loading = true;
			
			this.$iBox.http('getSelfWashOrderList', this.params)({
				method: 'post'
			}).then(res => {
				if (this.params.pageNumber == 1) {
					this.orderList = res.data.list;
				} else {
					this.orderList = this.orderList.concat(res.data.list);
				}
				
				this.hasMore = this.orderList.length < res.data.total;
				this.loading = false;
			}).catch(err => {
				this.loading = false;
				console.log('加载订单列表失败', err);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			});
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return;
			
			this.params.pageNumber++;
			this.loadOrderList();
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				1: 'status-processing',
				2: 'status-completed',
				3: 'status-cancelled'
			};
			return statusMap[status] || '';
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				1: '进行中',
				2: '已完成',
				3: '已取消'
			};
			return statusMap[status] || '未知';
		},
		
		// 获取支付方式文本
		getPayTypeText(payType) {
			const payTypeMap = {
				1: '账户余额',
				2: '洗车卡余额'
			};
			return payTypeMap[payType] || '未知';
		},
		
		// 跳转到订单详情
		toOrderDetail(order) {
			this.pushOrderDetail(order);
			uni.navigateTo({
				url: '/pages/selfWash/orderDetail/orderDetail'
			});
		},
		
		// 继续洗车
		continueWash(order) {
			if (!this.checkLogin()) return;

			uni.navigateTo({
				url: `/pages/selfWash/billing/billing?deviceId=${order.deviceId}&orderId=${order.id}`
			});
		},
		
		// 再次下单
		reorder(order) {
			uni.navigateTo({
				url: `/pages/selfWash/billing/billing?deviceId=${order.deviceId}`
			});
		},
		
		// 评价
		evaluate(order) {
			if (!this.checkLogin()) return;

			this.evaluateData.orderId = order.id;
			this.evaluateData.rating = 5;
			this.evaluateData.comment = '';
			this.showEvaluate = true;
		},
		
		// 提交评价
		submitEvaluate() {
			if (!this.evaluateData.comment.trim()) {
				uni.showToast({
					title: '请输入评价内容',
					icon: 'none'
				});
				return;
			}
			
			const params = {
				orderId: this.evaluateData.orderId,
				rating: this.evaluateData.rating,
				comment: this.evaluateData.comment
			};
			
			this.$iBox.http('submitSelfWashEvaluate', params)({
				method: 'post'
			}).then(res => {
				this.showEvaluate = false;
				uni.showToast({
					title: '评价成功',
					icon: 'success'
				});
				
				// 更新订单列表
				this.params.pageNumber = 1;
				this.loadOrderList();
			}).catch(err => {
				console.log('提交评价失败', err);
				uni.showToast({
					title: '评价失败',
					icon: 'none'
				});
			});
		},

		// 统一的登录检查方法
		checkLogin() {
			if (!this.userInfo.phone) {
				this.hackReset = true;
				this.if_login = true;
				return false;
			}
			return true;
		},

		// 登录成功回调
		loginSucess() {
			this.hackReset = false;
			this.if_login = false;
			// 刷新订单列表
			this.loadOrderList();
		}
	}
}
</script>

<style lang="scss">
page {
	background: #F4F6F8;
}

.orders-container {
	min-height: 100vh;
	
	.filter-tabs {
		background: #FFFFFF;
		display: flex;
		padding: 0 24rpx;
		position: sticky;
		top: 0;
		z-index: 100;

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 32rpx 0;
			font-size: 28rpx;
			color: #666666;
			position: relative;

			&.active {
				color: #00C7A3;
				font-weight: 600;

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 4rpx;
					background: #00C7A3;
					border-radius: 2rpx;
				}
			}
		}
	}

	.order-list {
		height: calc(100vh - 120rpx);
		padding: 24rpx;

		.order-item {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 24rpx;
			overflow: hidden;

			.order-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 30rpx;
				background: #FFF6ED;

				.order-info {
					.order-title {
						font-size: 26rpx;
						color: #FF7E32;
						margin-right: 16rpx;
					}

					.order-no {
						font-size: 24rpx;
						color: #FF7E32;
					}
				}

				.order-status {
					font-size: 24rpx;

					&.status-processing {
						color: #00C7A3;
					}

					&.status-completed {
						color: #666666;
					}

					&.status-cancelled {
						color: #FF6B6B;
					}
				}
			}

			.order-content {
				padding: 30rpx;

				.shop-info {
					display: flex;
					margin-bottom: 24rpx;

					.shop-image {
						width: 100rpx;
						height: 100rpx;
						border-radius: 12rpx;
						overflow: hidden;
						margin-right: 20rpx;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.shop-details {
						flex: 1;

						.shop-name {
							display: block;
							font-size: 30rpx;
							font-weight: 600;
							color: #222222;
							margin-bottom: 8rpx;
						}

						.device-name, .wash-time {
							display: block;
							font-size: 24rpx;
							color: #999999;
							margin-bottom: 6rpx;
						}
					}
				}

				.order-details {
					margin-bottom: 24rpx;

					.detail-row {
						display: flex;
						margin-bottom: 12rpx;

						.detail-label {
							font-size: 26rpx;
							color: #666666;
							width: 160rpx;
						}

						.detail-value {
							font-size: 26rpx;
							color: #222222;
							flex: 1;
						}
					}
				}

				.cost-info {
					border-top: 1px solid #EFEFEF;
					padding-top: 20rpx;

					.cost-row {
						display: flex;
						justify-content: space-between;
						margin-bottom: 12rpx;

						.cost-label {
							font-size: 26rpx;
							color: #666666;
						}

						.cost-value {
							font-size: 26rpx;
							color: #222222;

							&.discount {
								color: #00C7A3;
							}
						}
					}

					.cost-total {
						display: flex;
						justify-content: space-between;
						padding-top: 12rpx;
						border-top: 1px solid #EFEFEF;

						.total-label {
							font-size: 28rpx;
							font-weight: 600;
							color: #222222;
						}

						.total-value {
							font-size: 32rpx;
							font-weight: 600;
							color: #FF7E32;
						}
					}
				}
			}

			.order-actions {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 30rpx;
				border-top: 1px solid #EFEFEF;

				.action-left {
					.create-time {
						font-size: 24rpx;
						color: #999999;
					}
				}

				.action-right {
					display: flex;
					gap: 16rpx;
				}
			}
		}

		.load-more, .no-more {
			text-align: center;
			padding: 40rpx 0;
			color: #999999;
			font-size: 24rpx;

			.load-text {
				margin-left: 16rpx;
			}
		}
	}

	.empty-state {
		padding: 100rpx 0;
	}

	.evaluate-popup {
		width: 600rpx;
		padding: 40rpx;

		.evaluate-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #222222;
			text-align: center;
			margin-bottom: 40rpx;
		}

		.evaluate-content {
			.rate-section {
				margin-bottom: 30rpx;

				.rate-label {
					display: block;
					font-size: 28rpx;
					color: #222222;
					margin-bottom: 16rpx;
				}
			}

			.comment-section {
				.comment-label {
					display: block;
					font-size: 28rpx;
					color: #222222;
					margin-bottom: 16rpx;
				}
			}
		}

		.evaluate-actions {
			display: flex;
			gap: 20rpx;
			margin-top: 40rpx;
		}
	}
}
</style>
