<template>
	<view>
		<view class=""
			style="height: auto;width: 702rpx;margin: 20rpx auto;display: flex;flex-direction: column;align-items: center;background-color: #FFFFFF;border-radius: 12rpx;">
			<view class=""
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<text style="font-size: 26rpx;color: #222222;">开票类型:</text>
				<view class=""
					style="margin-left: 40rpx;height: 55rpx;width: 180rpx;border:1px solid #00C7A3;color:#00C7A3;border-radius: 87rpx;display: flex;align-items: center;justify-content: center;font-size: 24rpx;">
					普通电子发票
				</view>
			</view>
			<view class=""
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<text style="font-size: 26rpx;color: #222222;">抬头类型:</text>
				<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
					<view class="" v-for="item in type" @click="chooseType(item)"
						:style="item.id==id?'border:1px solid #00C7A3;color:#00C7A3;':'border:1px solid #999999;color:#999999;'"
						style="height: 55rpx;width: 120rpx;border-radius: 87rpx;display: flex;align-items: center;justify-content: center;font-size: 24rpx;margin-right: 30rpx;">
						{{item.name}}
					</view>
				</view>

			</view>
			<view class=""
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">
					<text style="color: red;">*</text>
					<text style="font-size: 26rpx;color: #222222;">抬头名称:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 400rpx;font-size: 26rpx;" v-model="title" placeholder="请输入抬头名称" />
				</view>
			</view>
			<view class="" v-if="id==1"
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">
					<text style="color: red;">*</text>
					<text style="font-size: 26rpx;color: #222222;">企业税号:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 400rpx;font-size: 26rpx;" v-model="comNumber"
						placeholder="请输入企业税号" />
				</view>
			</view>
			<view class=""
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">
					<text style="color: red;">*</text>
					<text style="font-size: 26rpx;color: #222222;">手机号码:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 340rpx;font-size: 26rpx;" v-model="phone" placeholder="请输入手机号码" />
				</view>
				<uv-button type="primary" size="mini" open-type="getPhoneNumber"
					:customStyle="{'width':'fit-content','background':'#00C7A3','z-index':999999,'height':'60rpx','border':'none'}"
					@getphonenumber="getPhoneNumber"><text style="font-size: 24rpx;">快速获取</text></uv-button>
			</view>
			<view class=""
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">
					<text style="color: red;">*</text>
					<text style="font-size: 26rpx;color: #222222;">电子邮箱:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 400rpx;font-size: 26rpx;" v-model="email" placeholder="请输入电子邮箱" />
				</view>
			</view>
			<view class="" v-if="id==1"
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">

					<text style="font-size: 26rpx;color: #222222;">开户银行:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 400rpx;font-size: 26rpx;" v-model="bankName"
						placeholder="请输入开户行" />
				</view>
			</view>

			<view class="" v-if="id==1"
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">

					<text style="font-size: 26rpx;color: #222222;">银行帐户:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 400rpx;font-size: 26rpx;" v-model="bankNumber"
						placeholder="请输入银行帐户" />
				</view>
			</view>

			<view class="" v-if="id==1"
				style="height: 115rpx;width: 100%;border-radius: 12rpx;display: flex;align-items: center;padding: 0 30rpx;border: 1px solid #EFEFEF;">
				<view class="" style="display: flex;align-items: center;">

					<text style="font-size: 26rpx;color: #222222;">企业地址:</text>
				</view>

				<view class="" style="margin-left: 40rpx;">
					<input type="text" style="width: 400rpx;font-size: 26rpx;" v-model="bankNumber"
						placeholder="请输入企业地址" />
				</view>
			</view>
		</view>

		<view class=""
			style="height: 148rpx;width: 100%;display: flex;align-items: center;justify-content: center;position: fixed;bottom: 0;">
			<view class="" @click="sure"
				style="height: 88rpx;width: 680rpx;border-radius: 87rpx;background-color: #00C7A3;color: #FFFFFF;display: flex;align-items: center;justify-content: center;">
				确认添加
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				type: [{
					id: 1,
					name: '企业'
				}, {
					id: 2,
					name: '个人'
				}],
				id: 1,
				title: '',
				comNumber: '',
				phone: '',
				email: '',
				bankName: '',
				bankNumber: '',
				address: '',

			}
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		methods: {
			chooseType(e) {
				this.id = e.id
			},
			getPhoneNumber(e) {
				console.log(e);
				if (e.errMsg === "getPhoneNumber:ok") {
					this.$iBox.http('getUserPhone', {
						iv: e.iv,
						encData: e.encryptedData

					})({
						method: 'post'
					}).then(res => {
						this.phone = res.data

					})
				}
			},
			sure() {
				if (!this.title) {
					uni.showToast({
						icon: 'none',
						title: '请填写抬头名称'
					})
					return
				}

				if (!this.comNumber && this.id == 1) {
					uni.showToast({
						icon: 'none',
						title: '请填写手机号'
					})
					return
				}

				if (!this.email) {
					uni.showToast({
						icon: 'none',
						title: '请填写电子邮箱'
					})
					return
				}

				if (!this.phone) {
					uni.showToast({
						icon: 'none',
						title: '请填写手机号'
					})
					return
				}
				
				

				let params = {}
				
				if(this.id == 1){
					params = {
						invoiceType: this.id,
						
						name: this.title,
						email: this.email,
						tel: this.phone,
						companyNumber:this.comNumber,
						bankNumber:this.bankNumber,
						bankName:this.bankName,
						address: this.address
					}
				}else{
					params = {
						invoiceType: this.id,
						
						name: this.title,
						email: this.email,
						tel: this.phone
					}
				}

				this.$iBox.http('saveUserInvoice', params)({
					method: 'post'
				}).then(res => {
					uni.navigateBack()
				})

			}
		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #F4F6F8;
	}
</style>