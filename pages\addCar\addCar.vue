<template>
	<view class="">
		<view class="" style="padding: 30rpx;width: 700rpx;background-color: #FFFFFF;margin: 30rpx;" v-for="item in carList">
			
			<view class="" style="display: flex;align-items: center;justify-content: space-between;">
				<p style="font-size: 32rpx;font-weight: 600;">车辆号码</p>
				<p style="font-size: 28rpx;color: #999999;" @click="delCar(item)">删除</p>
			</view>
			<view class="" style="margin-top: 10rpx;font-size: 32rpx;width: fit-content;padding: 10rpx 20rpx;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;
			background-color: #C8FFEF;">
				<text>{{item.carNumber}}</text>
			</view>
		</view>

		<!-- 添加车牌弹窗 -->
		<keyboard-plate ref="plateNumber" :plateNum.sync='plateNum' @change="getPlateNum" isShow></keyboard-plate>


		<view class=""
			style="position: fixed;bottom: 30rpx;left: 0;right: 0;width: 100%;height:100rpx;background: #FFFFFF;display: flex;align-items: center;justify-content: center;">
			<view class="" style="width: 600rpx;">
				<uv-button type="warning " color="#00C7A3" @click="carInputClick" text="添加车牌"></uv-button>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	export default {
		components: {

		},
		data() {
			return {
				plateNum: '',
				carList: [],
				checked: false,
			}
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		watch: {
			carList() {

			}
		},
		onLoad() {

		},
		onShow() {
			this.$iBox.http('getUserCar', {})({
					method: 'get'
				})
				.then(res => {
					this.carList = res.data
				})
				.catch(function(error) {
					console.log('网络错误', error);
				});
		},
		methods: {
			...mapActions('login', ['PUSHCAR']),
			carInputClick() {
				this.$refs.plateNumber.open();
			},
			ifCheck(e) {
				if (e) {
					return true
				} else {
					return false
				}
			},
			getPlateNum(e) {
				console.log('车牌号是：' + e.value, e.auto)
				this.plateNum = e.value
				this.checked = e.auto
				if (this.plateNum <= 7) {
					uni.showToast({
						icon: "none",
						title: '请添加正确的车牌'
					})
				} else {

					let params = {
						autoPay: this.checked ? 1 : 0,
						carNumber: this.plateNum,
					}

					this.$iBox.http('addUserCar', params)({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getUserCar', {})({
									method: 'get'
								})
								.then(res => {
									this.carList = res.data
								})
								.catch(function(error) {
									console.log('网络错误', error);
								});
						})
						.catch(function(error) {
							console.log('网络错误', error);
						});


				}
			},
			chooseCar(e) {
				this.PUSHCAR(e)
				uni.navigateBack({

				})
				console.log(e, 'erere');
			},
			add() {

			},

			updateaoto(status) {
				console.log(status, 'updata');
				let params = {
					autoPay: status.autoPay ? 1 : 0,
					id: status.id,
					carNumber: status.carNumber
				}
				if (status.autoPay) {
					uni.showModal({
						title: '提示',
						content: '自动扣费充值用户有效，余额需满足洗车费。默认快洗模式。?',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								this.$iBox.http('updateUserCar', params)({
										method: 'post'
									})
									.then(res => {
										this.$iBox.http('getUserCar', {})({
												method: 'get'
											})
											.then(res => {
												this.carList = res.data
											})
											.catch(function(error) {
												console.log('网络错误', error);
											});


									})
									.catch(function(error) {
										console.log('网络错误', error);
									});
							} else if (res.cancel) {
								console.log('用户点击取消')
							}
						}

					})
				} else {
					this.$iBox.http('updateUserCar', params)({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getUserCar', {})({
									method: 'get'
								})
								.then(res => {
									this.carList = res.data
								})
								.catch(function(error) {
									console.log('网络错误', error);
								});


						})
						.catch(function(error) {
							console.log('网络错误', error);
						});
				}

				console.log(status);
			},
			delCar(e) {
				let params = {
					id: e.id
				}
				uni.showModal({
					title: '提示',
					content: '是否删除该车牌?',
					success: (res) => {
						if (res.confirm) {
							this.$iBox.http('delUserCar', params)({
									method: 'delete'
								})
								.then(res => {
									this.$iBox.http('getUserCar', {})({
											method: 'get'
										})
										.then(res => {
											this.carList = res.data
										})
										.catch(function(error) {
											console.log('网络错误', error);
										});


								})
								.catch(function(error) {
									console.log('网络错误', error);
								});
						} else if (res.cancel) {
							console.log('用户点击取消')
						}
					}

				})
			}
		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>