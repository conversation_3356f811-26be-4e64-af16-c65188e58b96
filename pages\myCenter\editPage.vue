<template>
	<view>
		<view class=""
			style="background-color: #FFFFFF;padding:0 32rpx;border-radius:32rpx;height: 112rpx;display: flex;
			align-items: center;border-bottom:1px solid rgba(211, 215, 224, 1);margin: 20rpx auto;width: 620rpx;justify-content: space-between;">
			<input v-if="type=='name'" style="font-size: 30rpx;height: 100rpx;width: 500rpx;" placeholder="姓名"
				type="text" placeholder-style="color:rgba(153,153,153,1)" v-model="name"></input>
			<input v-if="type=='phone'" style="font-size: 30rpx;height: 100rpx;width: 500rpx;" placeholder="电话"
				type="text" placeholder-style="color:rgba(153,153,153,1)" v-model="phone"></input>
			<view v-if="type=='gender'"
				style="font-size: 30rpx;height: 100rpx;width: 500rpx;display: flex;align-items: center;"
				@click="choose">{{gender?gender:'暂无'}}</view>
			<input v-if="type=='weixin'"
				style="font-size: 30rpx;height: 100rpx;width: 500rpx;display: flex;align-items: center;"
				placeholder="微信号" type="text" placeholder-style="color:rgba(153,153,153,1)" v-model="wx_number"></input>
			<uv-picker ref="picker" :columns="columns" @confirm="confirm"></uv-picker>
			<text style="font-size: 34rpx;color: #689198;" v-if="type=='name'||type=='weixin'||type=='gender'"
				@click="sureEdit">确定</text>
			<uv-button type="primary" size="mini" open-type="getPhoneNumber" v-if="type=='phone'"
				:customStyle="{'width':'fit-content','background':'#689198','z-index':999999,'height':'60rpx','border':'none'}"
				@getphonenumber="getPhoneNumber"><text style="font-size: 24rpx;">快速获取</text></uv-button>
		</view>
		<view class="" v-if="type=='phone'" @click="surePhone()" style="width: 400rpx;height: 96rpx;background: linear-gradient(270deg, #679098 0%, #96C4CC 100%);
			border: 1px solid rgba(46, 60, 62, 0.1);display: flex;align-items: center;justify-content: center;position: fixed;top: 220rpx;left: 0;right: 0;margin: 0 auto;
			border-radius: 48rpx;color: #FFFFFF;">
			确定
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				type: '',
				name: '',
				phone: '',
				gender: '',
				wx_number: '',
				params: null,
				columns: [
					['男', '女', '保密']
				],
				centerUser:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
		},
		onLoad(options) {
			console.log(options);
			this.params = JSON.parse(options.type)
			this.type = this.params.type
			if (this.type == 'name') {
				this.name = this.params.value
			} else if (this.type == 'phone') {
				this.phone = this.params.value
			} else if (this.type == 'gender') {
				this.gender = this.params.value
			} else {
				this.wx_number = this.params.value
			}

		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			choose() {
				this.$refs.picker.open();
			},
			confirm(e) {
				console.log('confirm', e);
				this.gender = e.value[0]
			},
			sureEdit() {
				let params = ''
				if (this.type == 'name') {
					params = {
						user_name: this.name
					}
				} else if (this.type == 'phone') {
					params = {
						phone: this.phone
					}
				} else if (this.type == 'gender') {
					params = {
						gender: this.gender
					}
				} else {
					params = {
						wx_number: this.wx_number
					}
				}


				// 修改会员信息
				this.$iBox.http('updateUserInfo', params)({
					method: 'post'
				}).then(res => {
					this.$iBox.http('getUserInfo', {

					})({
						method: 'get'
					}).then(res => {
						uni.navigateBack()
					}).catch(function(error) {
						console.log('网络错误', error)
					})

				})
			},
			surePhone(){
				
				let params = {
					phone: this.phone
				}
				// 修改会员信息
				this.$iBox.http('updateUserInfo', params)({
					method: 'post'
				}).then(res => {
					this.$iBox.http('getUserInfo', {
				
					})({
						method: 'get'
					}).then(res => {
						this.centerUser = res.data
						this.centerUser.token = this.userInfo.token
						this.centerUser.session_key = this.userInfo.session_key
						this.updateUserInfo(this.centerUser)
						uni.navigateBack()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				
				})
			},
			getPhoneNumber(e) {
				console.log(e);
				if (e.errMsg === "getPhoneNumber:ok") {
					this.$iBox.http('getUserPhone', {
						iv: e.iv,
						encData: e.encryptedData

					})({
						method: 'post'
					}).then(res => {
						this.phone = res.data
						let params = {
							phone: this.phone
						}
						// 修改会员信息
						this.$iBox.http('updateUserInfo', params)({
							method: 'post'
						}).then(res => {
							this.$iBox.http('getUserInfo', {

							})({
								method: 'get'
							}).then(res => {
								this.centerUser = res.data
								this.centerUser.token = this.userInfo.token
								this.centerUser.session_key = this.userInfo.session_key
								this.updateUserInfo(this.centerUser)
								uni.navigateBack()
							}).catch(function(error) {
								console.log('网络错误', error)
							})

						})
					})
				}
			},
		}
	}
</script>
<style>
	page {
		background: #EBEFFA;
	}
</style>
<style scoped lang="scss">

</style>