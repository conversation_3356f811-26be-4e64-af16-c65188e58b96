<template>
	<view>
		<view class=""
			style="padding: 30rpx;display: flex;flex-direction: column;align-items: center;background-color: #F4F6F8;">
			<view class="listItem" v-for="(item, index) in list" :key="item.id" @click="toDetail(item)"
				:style="index==0&&source == 'index'?'background-color: #C8FFEF;padding:0;':''">
				<view class=""
					style="min-height: 202rpx;width: 100%;display: flex;flex-direction: column;justify-content: space-between;padding: 8rpx;align-items: center;position: relative;"
					v-if="index==0&&source == 'index'">
					<view class="" style="position: absolute;top: 4rpx;right: 20rpx;">
						<image src="/static/images/car.png" style="width: 155rpx;height: 52rpx;" mode=""></image>
					</view>
					<view class=""
						style="height: 58rpx;width: 644rpx;display: flex;align-items: center;color: #077E6C;">
						<uv-icon name="thumb-up-fill" color="#077E6C" size="28"></uv-icon>
						<image src="../../static/images/tj.png" style="height: 32rpx;width: 70rpx;" mode=""></image>
						<text style="padding: 0 10rpx;font-size: 24rpx;">|</text>
						<text style="font-size: 24rpx;">{{text}}·即插即用</text>
					</view>
					<view class=""
						style="min-height: 306rpx;background-color: #FFFFFF;width: 100%;border-radius: 16rpx;padding: 30rpx;position: relative;">
						<view class=""
							style="display: flex;width: 100%;align-items: center;justify-content: space-between;">
							<text style="color: #222222;font-size: 32rpx;">{{item.parkName}}</text>
							<text
								style="color: #FF7E32;">{{item.distance<1?parseInt(item.distance*1000)+'m':parseInt(item.distance)+'Km'}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 10rpx;">
							<view class="" style="display: flex;align-items: center;padding-left: 70rpx;width: 100%;">
								<view class="" v-for="item in detail.equipmentList" style="width: fit-content;padding: 9rpx 12rpx;border-radius: 8rpx;background-color: #EFEFEF;
								display: flex;align-items: center;justify-content: center;font-size: 22rpx;color: #999999;margin-right: 20rpx;">
									{{item.serviceName}}
								</view>
							</view>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 10rpx;">
							<view class="" style="margin-right: 4rpx;width: 32rpx;height: 32rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: #5ec3a5;color: #FFFFFF;font-size: 30rpx;">
								<text style="font-size: 20rpx;">P</text>
							</view>
							<view class="" v-for="item1 in item.serviceList"
								style="width: fit-content;font-family: 24rpx;display: flex;align-items: center;justify-content: center;margin-right: 10rpx;">
								<text style="color: #222222;font-size: 24rpx;">{{item1.serviceName}}</text>
							</view>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 10rpx;" v-if="item.parkServiceList.length>0">
							<view class="" style="margin-right: 10rpx;width: 32rpx;height: 32rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: #fa2729;color: #FFFFFF;font-size: 30rpx;">
								<text style="font-size: 20rpx;">占</text>
							</view>
							<view class="" v-for="item1 in item.parkServiceList"
								style="height: 32rpx;width: fit-content;font-family: 24rpx;display: flex;align-items: center;justify-content: center;margin-right: 10rpx;">
								<text style="color: #222222;font-size: 24rpx;">{{item1.serviceName}}</text>
							</view>
						</view>

						<view class="" style="margin-top: 14rpx;display: flex;align-items: center;">
							<view class="" v-if="item.wxDeviceFastCount>0"
								style="min-width: 160rpx;height: 34rpx;background-color: #FF3B3B14;display: flex;align-items: center;">
								<view class=""
									style="width: 36rpx;height: 36rpx;border-radius: 6rpx;background-color: #FF7E32;display: flex;align-items: center;justify-content: center;">
									<image src="@/static/images/chao.png" style="width: 26rpx;height: 22rpx;" mode="">
									</image>

								</view>
								<view class="" style="padding: 0 10rpx;display: flex;align-items: center;width: fit-content;">
									<text style="font-size: 22rpx;color: #FF7E32;">闲 {{item.deviceFastFreeCount}}</text>
								</view>
								<text style="color: #FF7E32;font-size: 22rpx;">/</text>
								<view class="" style="padding: 0 10rpx;display: flex;align-items: center;width: fit-content;">

									<text style="color: #FF7E3266;font-size: 22rpx;">{{item.wxDeviceFastCount}}</text>
								</view>
							</view>
							<view class="" v-if="item.wxDeviceSlowCount>0"
								style="min-width: 160rpx;height: 34rpx;background-color: #1477FF14;display: flex;align-items: center;margin-left: 30rpx;">
								<view class=""
									style="width: 36rpx;height: 36rpx;border-radius: 6rpx;background-color: #0095F2;display: flex;align-items: center;justify-content: center;">
									<image src="@/static/images/man.png" style="width: 26rpx;height: 22rpx;" mode="">
									</image>

								</view>
								<view class="" style="padding: 0 10rpx;display: flex;align-items: center;width: fit-content;">
									<text style="font-size: 22rpx;color: #0095F2;">闲 {{item.deviceSlowFreeCount}}</text>
								</view>
								<text style="color: #0095F2;font-size: 22rpx;">/</text>
								<view class="" style="padding: 0 10rpx;display: flex;align-items: center;width: fit-content;">

									<text style="color: #0095F266;font-size: 22rpx;">{{item.wxDeviceSlowCount}}</text>
								</view>
							</view>
						</view>
						<view class=""
							style="width: 610rpx;display: flex;justify-content: space-between;align-items: center;margin-top: 20rpx;">
							<view class="">
								<view class="" style="display: flex;align-items: center;">
									<text
										style="font-size: 48rpx;color: #FF7E32;font-weight: 600;" v-if="item.equipmentPolicy">{{item.equipmentPolicy?(item.equipmentPolicy.elecPrice+item.equipmentPolicy.sevicePrice).toFixed(4):'0'}}</text>
									<text style="color: #FF7E32;font-size: 18rpx;margin-left: 8rpx;margin-right: 30rpx;">元/度</text>
									
								</view>
								<view class="" style="display: flex;align-items: center;justify-content: space-between;height: 50rpx;">
									<view class="" style="width: 262rpx;height: 34rpx;background: linear-gradient(90deg, #7C859E 0%, #BDC5DE 100%);display: flex;align-items: center;
									border-radius: 8rpx;justify-content: space-between;padding: 0 4rpx;margin-right: 20rpx;">
										<text style="font-size: 18rpx;color: #FFFFFF;padding-left: 8rpx;">服务费</text>
										<view class=""
											style="width: 160rpx;height: 28rpx;border-radius: 4rpx;background-color: #FFFFFF;display: flex;align-items: center;padding: 0 8rpx;">
											<text
												style="color: #999999;font-size: 18rpx;">{{item.equipmentPolicy.sevicePrice}}元/度KWh</text>
										</view>
									</view>
									<uv-tags v-if="item.discountRate>0" :text="'服务费'+item.discountRate*10+'折'" type="error" size="mini" plain></uv-tags>
								</view>
								
							</view>
							
						</view>
						<view class=""  @click.stop="toMap(item)"
							style="position: absolute;bottom:100rpx;right:30rpx;width: 150rpx;height: 60rpx;display: flex;align-items: center;justify-content: center;background-color: #FF7E32;border-radius: 60rpx;">
							<image src="/static/images/dh.png" style="width: 24rpx;height: 24rpx;" mode=""></image>
							<text
								style="font-size: 32rpx;font-weight: 400;color: #FFFFFF;margin-left: 10rpx;">导航</text>
						</view>
					</view>
				</view>
				<view class="" v-if="index!=0||(source!='index')"
					style="padding: 30rpx;background-color: #FFFFFF;width: 100%;border-radius: 16rpx;padding: 30rpx;position: relative;min-height: 202rpx;">
					<view class=""
						style="display: flex;width: 100%;align-items: center;justify-content: space-between;">
						<view class="" style="display: flex;align-items: center;">
							<image src="/static/images/rgicon.png" style="width: 48rpx;height: 48rpx;" mode=""></image>
							<text style="color: #222222;font-size: 32rpx;">{{item.parkName}}</text>
						</view>

						<text
							style="color: #FF7E32;">{{item.distance<1?parseInt(item.distance*1000)+'m':parseInt(item.distance)+'Km'}}</text>
					</view>
					<view class="" style="display: flex;align-items: center;margin-top: 10rpx;">
						<view class="" style="display: flex;align-items: center;width: 100%;">
							<view class="" v-for="item1 in item.equipmentList" style="width: fit-content;padding: 9rpx 12rpx;border-radius: 8rpx;background-color: #EFEFEF;
							display: flex;align-items: center;justify-content: center;font-size: 22rpx;color: #222222;margin-right: 20rpx;">
								{{item1.serviceName}}
							</view>
						</view>
					</view>
					<view class="" style="display: flex;align-items: center;margin-top: 10rpx;">
						<view class="" style="margin-right: 4rpx;width: 32rpx;height: 32rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: #5ec3a5;color: #FFFFFF;font-size: 30rpx;">
							<text style="font-size: 20rpx;">P</text>
						</view>
						<view class="" v-for="item1 in item.serviceList"
							style="min-height: 32rpx;width: fit-content;font-family: 24rpx;display: flex;align-items: center;justify-content: center;margin-right: 10rpx;">
							<text style="color: #222222;font-size: 24rpx;">{{item1.serviceName}}</text>
						</view>
					</view>
					<view class="" style="display: flex;align-items: center;margin-top: 10rpx;" v-if="item.parkServiceList.length>0">
						<view class="" style="margin-right: 10rpx;width: 32rpx;height: 32rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: #fa2729;color: #FFFFFF;font-size: 30rpx;">
							<text style="font-size: 20rpx;">占</text>
						</view>
						<view class="" v-for="item1 in item.parkServiceList"
							style="min-height: 32rpx;width: fit-content;font-family: 24rpx;display: flex;align-items: center;justify-content: center;margin-right: 10rpx;">
							<text style="color: #222222;font-size: 24rpx;">{{item1.serviceName}}</text>
						</view>
					</view>
					<view class="" style="margin-top: 14rpx;display: flex;align-items: center;">
						<view class="" v-if="item.wxDeviceFastCount>0"
							style="min-width: 160rpx;height: 34rpx;background-color: #FF3B3B14;display: flex;align-items: center;">
							<view class=""
								style="width: 36rpx;height: 36rpx;border-radius: 6rpx;background-color: #FF7E32;display: flex;align-items: center;justify-content: center;">
								<image src="@/static/images/chao.png" style="width: 26rpx;height: 22rpx;" mode="">
								</image>

							</view>
							<view class="" style="padding: 0 10rpx;display: flex;align-items: center;">
								<text style="font-size: 22rpx;color: #FF7E32;">闲 {{item.deviceFastFreeCount}}</text>
							</view>
							<text style="color: #FF7E32;font-size: 22rpx;">/</text>
							<view class="" style="padding: 0 10rpx;display: flex;align-items: center;">

								<text style="color: #FF7E3266;font-size: 22rpx;">{{item.wxDeviceFastCount}}</text>
							</view>
						</view>
						<view class="" v-if="item.wxDeviceSlowCount>0"
							style="min-width: 160rpx;height: 34rpx;background-color: #1477FF14;display: flex;align-items: center;margin-left: 30rpx;">
							<view class=""
								style="width: 36rpx;height: 36rpx;border-radius: 6rpx;background-color: #0095F2;display: flex;align-items: center;justify-content: center;">
								<image src="@/static/images/man.png" style="width: 26rpx;height: 22rpx;" mode="">
								</image>

							</view>
							<view class="" style="padding: 0 10rpx;display: flex;align-items: center;">
								<text style="font-size: 22rpx;color: #0095F2;">闲 {{item.deviceSlowFreeCount}}</text>
							</view>
							<text style="color: #0095F2;font-size: 22rpx;">/</text>
							<view class="" style="padding: 0 10rpx;display: flex;align-items: center;">

								<text style="color: #0095F266;font-size: 22rpx;">{{item.wxDeviceSlowCount}}</text>
							</view>
						</view>
					</view>
					<view class=""
						style="width: 610rpx;display: flex;justify-content: space-between;align-items: center;margin-top: 20rpx;">
						<view class="">
							<view class="" style="display: flex;align-items: center;">
								<text
									style="font-size: 48rpx;color: #FF7E32;font-weight: 600;" v-if="item.equipmentPolicy">{{item.equipmentPolicy?(item.equipmentPolicy.elecPrice+item.equipmentPolicy.sevicePrice).toFixed(4):'0'}}</text>
								<text style="color: #FF7E32;font-size: 18rpx;margin-left: 8rpx;margin-right: 30rpx;">元/度</text>
								
							</view>
							<view class="" style="display: flex;align-items: center;height: 50rpx;">
								<view class="" style="width: 262rpx;height: 34rpx;background: linear-gradient(90deg, #7C859E 0%, #BDC5DE 100%);display: flex;align-items: center;
								border-radius: 8rpx;justify-content: space-between;padding: 0 4rpx;margin-right: 20rpx;">
									<text style="font-size: 18rpx;color: #FFFFFF;padding-left: 8rpx;">服务费</text>
									<view class=""
										style="width: 160rpx;height: 28rpx;border-radius: 4rpx;background-color: #FFFFFF;display: flex;align-items: center;padding: 0 8rpx;">
										<text
											style="color: #999999;font-size: 18rpx;">{{item.equipmentPolicy?item.equipmentPolicy.sevicePrice:''}}元/度KWh</text>
									</view>
								</view>
								<uv-tags v-if="item.discountRate>0" :text="'服务费'+item.discountRate*10+'折'" type="error" size="mini" plain></uv-tags>
							</view>
						</view>
					</view>
					<view class=""  @click.stop="toMap(item)"
						style="position: absolute;bottom:100rpx;right:30rpx;width: 150rpx;height: 60rpx;display: flex;align-items: center;justify-content: center;background-color: #FF7E32;border-radius: 60rpx;">
						<image src="/static/images/dh.png" style="width: 24rpx;height: 24rpx;" mode=""></image>
						<text
							style="font-size: 32rpx;font-weight: 400;color: #FFFFFF;margin-left: 10rpx;">导航</text>
					</view>
				</view>

			</view>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-powerList",
		data() {
			return {

			};
		},
		props: {
			list: {
				type: Array,
				default: []
			},
			source: {
				type: String,
				default: ''
			},
			text: {
				type: String,
				default: ''
			}

		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		watch: {
			list: {
				handler(newVal, oldVal) {
					console.log(newVal, 'newVal');
				}
			}
		},
		methods: {
			...mapActions('login', ['toLogin', 'pushShopList', 'pushShop']),
			toDetail(e) {
				this.pushShop(e)
				uni.navigateTo({
					url: '/pages/index/detail/detail'
				})
			},
			toMap(e) {
				wx.openLocation({
					latitude:e.latitude,
					longitude:e.longitude,
					scale: 18,
					name: e.parkName, // 位置名
					address: e.address // 地址的详细说明
				})
			}
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">
	.listItem {
		width: 686rpx;
		// height: 400rpx;
		border-radius: 16rpx;
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
		// padding: 30rpx;
	}
</style>