<template>
	<view>
		<view v-for="(item, index) in couponList" :key="index" class="coupon_box">
			<view class="left">
				<view class="left_top">
					<text class="hui_name">{{item.couponName}}</text>
					<!-- <text class="hui_name">满{{item.money}}减{{item.reduce}}</text> -->
				</view>
				<view class="left_bottom">
					<p>有效日期：{{item.startTime.split(' ')[0]}}至{{item.endTime.split(' ')[0]}}</p>
					<p>详情：{{item.description}}</p>
				</view>
				<!-- <image src="./ylq.png" v-if="item.status == 1" class="ylq"></image> -->
			</view>
			<view class="right">
				<view class="money">￥{{item.amount}}</view>
				<text>满{{item.useCondition}}可用</text>
			</view>

			<view class="bottom">
				<p v-if="source=='center'">可用店:</p>
				<view v-for="(row, indexs) in item.shopList" v-if="source=='center'" :key="indexs">
					{{row.shopName}}
				</view>
				<view class="receiveBtn" v-if="source=='get'&&select.id!=item.id" style="background:#fbbd08"
					@tap="onreceive(item)">立即领取</view>
				<view class="receiveBtn" v-if="source=='center'" :style="{background:item.userCouponStatus==0 ? '#fbbd08':'#a9a9a9'}"
					@tap="onreceive(item, index)">{{item.userCouponStatus==0 ? '未使用':'已使用'}}</view>
				<view class="receiveBtn" v-if="source=='use'&&select.id!=item.id" style="background:#fbbd08"
					@tap="onreceive(item)">立即使用</view>
				<view class="receiveBtn" v-if="source=='use'&&select.id==item.id" style="background:#ff0000"
					@tap="onreceiveCancle(item)">取消选择</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				select: false
			};
		},

		components: {},
		props: {
			colors: {
				type: String
			},
			couponList: {
				type: Array
			},
			source: {
				type: String
			},
			isSelect: {
				type: Object
			}
		},
		mounted() {
			this.select = this.isSelect
		},
		methods: {
			onreceive(item) { //领取优惠券
				let that = this
				// 向上层传递领取优惠券
				this.$emit('onReceive', item)

			},
			onreceiveCancle(item) { //领取优惠券
				let that = this
				this.select = false
				this.$emit('onReceive', null)
			},

		}
	};
</script>
<style lang="scss" scoped>
	.coupon_box {
		margin: 20upx;
		padding: 20upx;
		box-shadow: 0upx 0upx 10upx #ddd;
		position: relative;
		border-radius: 10upx;
		padding-bottom: 10upx;
		overflow: hidden;

	}

	.coupon_box .left {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		border-bottom: 1upx solid #eee;
		padding-bottom: 20upx;
		position: relative;
	}

	.coupon_box .left .ylq {
		width: 60upx;
		height: 45upx;
		position: absolute;
		top: 0;
		right: 140upx;
	}

	.coupon_box .left .hui {
		width: 40upx;
		height: 40upx;
		font-size: 22upx;
		color: #fff;
		background-color: rgba(255, 84, 110, .8);
		border-radius: 8upx;
		line-height: 40upx;
		text-align: center;
		display: inline-block;
		transform: translateY(-5upx);
	}

	.coupon_box .left .left_top {
		width: 80vw;
		display: block;
		font-size: 26upx;
		font-weight: bold;

	}

	.left_top .hui_name {
		line-height: 60upx;
		height: 60upx;
		margin-left: 20upx;
		display: inline-block;
		font-size: 28upx;
	}

	.left_bottom {
		font-size: 24upx;
		font-weight: 500;
		color: #333;
		height: 120upx;
		line-height: 60upx;
	}

	.coupon_box .right {
		position: absolute;
		right: 20upx;
		top: 25upx;
		text-align: center;
	}

	.coupon_box .right .money {
		font-size: 45upx;
		margin-bottom: 10upx;
	}

	.coupon_box .right text {
		font-size: 24upx;
		color: #999;
	}

	.coupon_box .bottom {
		min-height: 60upx;
		line-height: 60upx;
		display: flex;
		align-content: flex-start;
		font-size: 24upx;
		margin-top: 10upx;
		flex-wrap: wrap;
	}

	.coupon_box .bottom view {
		margin-right: 20upx;
		color: #888;
	}

	.receiveBtn {
		position: absolute;
		left: calc(100vw - 86px);

		width: 58px;
		height: 24px;
		line-height: 24px;

		border-radius: 4px;
		background-color: #a6a6a6;
		color: white !important;

		font-size: 12px;
		text-align: center;
		margin-top: 2px;

	}
</style>