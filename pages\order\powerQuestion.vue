<template>
	<view>
		<view class="" style="padding: 36rpx;font-size: 36rpx;color: #222222;">
			问题具体描述
		</view>
		<view class="" style="width: 700rpx;margin: 20rpx auto;">
			<uv-textarea v-model="desc" height="200" count placeholder="请输入具体问题描述"></uv-textarea>
		</view>
	
		
		<view class="" style="width: 100%;padding: 0 30rpx;background-color: #FFFFFF;">
			<view class="" style="padding: 36rpx;font-size: 36rpx;color: #222222;">
				请上传图片说明
				<text style="font-size: 24rpx;color: #999999;">(最多上传9张)</text>
			</view>
			<view class="">
				<uv-album :urls="imgs" v-if="imgs"></uv-album>
			</view>
			<view class="">
				<uv-upload :fileList="fileList1" name="1" multiple :maxCount="9" @afterRead="afterRead" @delete="deletePic"
					:previewFullImage="true"></uv-upload>
			</view>
			
		</view>
		
		<view class="" @click="formit" style="width: 100%;height: 145rpx;position: fixed;bottom: 0;display: flex;align-items: center;justify-content: center;">
			<view class="" style="height: 88rpx;width: 680rpx;border-radius: 88rpx;background-color: #00C7A3;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
				提交
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				fileList1: [],
				imgList:[],
				action: '',
				desc:'',
				imgs:[],
				id:''
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop', 'orderDetail']),
		},
		onLoad(options) {
			this.id = options.id
			this.$iBox.http('getChargingPileOrderList', {orderId:this.id})({
				method: 'get'
			}).then(res => {
				if(res.data){
					this.desc = res.data.content
					if(res.data.images.length>0){
						this.imgs = res.data.images
					}
				}
				
				
				
			})
		},
		methods: {
			formit(){
				this.$iBox.http('addChargingOrderFeedback', {orderId:this.id,imagesList:this.imgList,content:this.desc})({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title:'提示',
						content:'添加成功',
						success() {
							uni.navigateBack()
						}
					})
				})
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					uni.getStorage({
						key: 'baseUrl',
						success: (res) => {
							console.log(res.data);
							this.action = res.data + '/resource/upload'
						}
					});
					console.log(url, this.userInfo);
					let a = uni.uploadFile({
						url: 'https://wx.kunfeizn.com/resource/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						header: {
							'Authorization': 'Bearer ' + this.userInfo.token
						},
						success: (res) => {
							setTimeout(() => {
								this.imgList.push(JSON.parse(res.data).data)
								resolve(res.data.data)
							}, 1000)
						},
						complete: (msg) => {
							console.log(msg);
						}
					});
				})
			}
		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>