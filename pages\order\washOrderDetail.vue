<template>
	<view>
		<view class="" style="position: fixed;top: -40rpx;right: -40rpx;z-index: 1;">
			<image src="https://www.kemanfang.net/xcx_resource/xcbj.png" style="height: 303rpx;width: 305rpx;" mode=""></image>
		</view>
		<view class="" style="padding: 40rpx 24rpx;display: flex;align-items: center;">
			<image src="/static/images/od.png" style="height: 36rpx;width: 36rpx;" mode=""></image>
			<text style="font-size: 36rpx;color: #F6F6F6;margin-left: 10rpx;">{{filterOrder(orderDetail.status)}}</text>
		</view>
		<view class="" style="width: 702rpx;margin: 20rpx auto;background-color: #FFFFFF;border-radius: 48rpx;">
			<view class="" style="width: 100%;height: 98rpx;display: flex;align-items: center;padding: 0 30rpx;font-size: 36rpx;color: #222222;">
				洗车信息
			</view>
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;"></view>
			<view class="" style="width: 100%;padding:0 30rpx;display: flex;flex-direction: column;">
				<view class="" style="font-size: 32rpx;font-weight: 600;color: #222222;">
					{{orderDetail.shopName}}
				</view>
			
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">订单编号:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.orderCode}}</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">车牌号码:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.carNumber}}</text>
				</view>
			</view>
			<view class="" style="height: 161rpx;display: flex;align-items: center;padding: 0 30rpx;justify-content: space-between;">
				<view class=" " style="display: flex;flex-direction: column;">
					<text style="font-size: 36rpx;font-weight: 600;">{{orderDetail.createTime.split(' ')[1].split(':')[0]+':'+orderDetail.createTime.split(' ')[1].split(':')[1]}}</text>
					<text style="font-size: 22rpx;color: #999999;">{{orderDetail.createTime.split(' ')[0]}}</text>
				</view>
				<view class="" style="width: 318rpx;height: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
					
					<view class="" style="height: 2px;width: 318rpx;background:linear-gradient(90deg, #9BCFF5 -5.1%, #2B67CA 100%);">
						
					</view>
				</view>
				<view class=" " style="display: flex;flex-direction: column;" v-if="orderDetail.finishTime">
					<text style="font-size: 36rpx;font-weight: 600;">{{orderDetail.finishTime.split(' ')[1].split(':')[0]+':'+orderDetail.finishTime.split(' ')[1].split(':')[1]}}</text>
					<text style="font-size: 22rpx;color: #999999;">{{orderDetail.finishTime.split(' ')[0]}}</text>
				</view>
				<view class="" style="display: flex;flex-direction: column;" v-else>
					<text>暂无结束时间</text>
				</view>
			</view>
		</view>
		
		<view class="" style="min-height: 380rpx;width: 702rpx;margin: 20rpx auto;background-color: #FFFFFF;border-radius: 48rpx;">
			<view class="" style="width: 100%;height: 98rpx;display: flex;align-items: center;padding: 0 30rpx;font-size: 36rpx;color: #222222;">
				费用明细
			</view>
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;"></view>
			<view class="" style="width: 100%;padding:0 30rpx;display: flex;flex-direction: column;">
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">洗车模式:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.washModeName}}</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">洗车费用:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.price}}元</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">卡券减免:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.discounts}}元</text>
				</view>
				
			</view>
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;margin: 30rpx;"></view>
			<view class="" style="display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;">
				<p style="font-size: 28rpx;font-weight: 600;color: #222222;">实付总费用</p>
				<view class="" style="color: #FF7E32;">
					<text style="font-size: 28rpx;font-weight: 600;">{{orderDetail.amount}}</text>
					<text style="font-size: 24rpx;">元</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop','orderDetail']),
		},
		
		methods: {
			filterOrder(e) {
				console.log(e,'dd');
				let list = [{
					id: 0,
					name:'未支付'
				}, {
					id: 1,
					name:'已支付'
				}, {
					id: 2,
					name:'已完成'
				}, {
					id: 3,
					name:'已过期'
				}, {
					id: 4,
					name:'正在申请'
				}, {
					id: 5,
					name:'已退款'
				}, {
					id: 6,
					name:'申请拒绝'
				}]
				let type = list.filter(item => {
					return item.id == e
				})[0].name
				
				return type
			},
		}
	}
</script>

<style>
	page {
		background: linear-gradient(180deg, #0095F2 0%, #F4F6F8 100%);
	}
	
	view {
		box-sizing: border-box;
	}
</style>
