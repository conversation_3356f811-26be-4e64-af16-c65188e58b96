<template>
	<view>
		<view class="" style="position: fixed;top: -40rpx;right: -40rpx;z-index: 1;">
			<image src="https://www.kemanfang.net/xcx_resource/xcbj.png" style="height: 303rpx;width: 305rpx;" mode=""></image>
		</view>
		<view class="" style="padding: 40rpx 24rpx;display: flex;align-items: center;">
			<image src="/static/images/od.png" style="height: 36rpx;width: 36rpx;" mode=""></image>
			<text style="font-size: 36rpx;color: #F6F6F6;margin-left: 10rpx;">{{orderDetail.chargeStatus==4?'已完成':'充电中'}}</text>
		</view>
		<view class="" style="min-height: 532rpx;width: 702rpx;margin: 20rpx auto;background-color: #FFFFFF;border-radius: 48rpx;">
			<view class="" style="width: 100%;height: 98rpx;display: flex;align-items: center;padding: 0 30rpx;font-size: 36rpx;color: #222222;">
				充电信息
			</view>
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;"></view>
			<view class="" style="width: 100%;padding:0 30rpx;display: flex;flex-direction: column;">
				<view class="" style="font-size: 32rpx;font-weight: 600;color: #222222;">
					{{orderDetail.stationName}}
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">设备编号:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.deviceName}}</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">订单编号:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.orderNo}}</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color: #CCCCCC;">车牌号码:</text>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.plateNo}}</text>
				</view>
			</view>
			<view class="" style="height: 161rpx;display: flex;align-items: center;padding: 0 30rpx;justify-content: space-between;">
				<view class=" " style="display: flex;flex-direction: column;">
					<text style="font-size: 36rpx;font-weight: 600;">{{orderDetail.startTime.split(' ')[1].split(':')[0]+':'+orderDetail.startTime.split(' ')[1].split(':')[1]}}</text>
					<text style="font-size: 22rpx;color: #999999;">{{orderDetail.startTime.split(' ')[0]}}</text>
				</view>
				<view class="" style="width: 318rpx;height: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<view class="" style="width: fit-content;padding: 4rpx 10rxp;font-size: 20rpx;color: #FF7E32;">
						{{orderDetail.chargeStatus==4?'已完成':'充电中'}}
						<text style="margin-left: 10rpx;">总用时长{{orderDetail.timeLen}}分钟</text>
					</view>
					<image src="/static/images/xt.png" style="height: 25rpx;width: 100%;" mode=""></image>
				</view>
				<view class=" " style="display: flex;flex-direction: column;">
					<text style="font-size: 36rpx;font-weight: 600;">{{orderDetail.endTime.split(' ')[1].split(':')[0]+':'+orderDetail.endTime.split(' ')[1].split(':')[1]}}</text>
					<text style="font-size: 22rpx;color: #999999;">{{orderDetail.endTime.split(' ')[0]}}</text>
				</view>
			</view>
		
			
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;margin-bottom: 20rpx;"></view>
			<view class="" style="display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;">
				<p style="font-size: 28rpx;font-weight: 600;color: #222222;">合计充电量</p>
				<view class="">
					<text style="font-size: 28rpx;font-weight: 600;color: #222222;">{{orderDetail.power}}</text>
					<text style="font-size: 24rpx;">度</text>
				</view>
			</view>
		</view>
		
		<view class="" style="min-height: 38rpx;width: 702rpx;margin: 20rpx auto;background-color: #FFFFFF;border-radius: 48rpx;">
			<view class="" style="width: 100%;height: 98rpx;display: flex;align-items: center;padding: 0 30rpx;font-size: 36rpx;color: #222222;">
				费用明细
			</view>
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;"></view>
			<view class="" style="width: 100%;padding:0 30rpx;display: flex;flex-direction: column;">
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<view style="color: #CCCCCC;width: 100rpx;">充电度数:</view>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.power}}</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<view style="color: #CCCCCC;width: 100rpx;">电费:</view>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.elecMoney}}元</text>
				</view>
				<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
					<view style="color: #CCCCCC;width: 100rpx;">服务费:</view>
					<text style="color: #222222;margin-left: 20rpx;">{{orderDetail.seviceMoney}}元</text>
				</view>
				
			</view>
			<view class="" style="width: 100%;height: 1px;background: #EFEFEF;margin: 30rpx;"></view>
			<view class="" style="display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;">
				<p style="font-size: 28rpx;font-weight: 600;color: #222222;">实付总费用</p>
				<view class="" style="color: #FF7E32;">
					<text style="font-size: 28rpx;font-weight: 600;">{{orderDetail.totalMoney}}</text>
					<text style="font-size: 24rpx;">元</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop','orderDetail']),
		},
		methods: {
			
		}
	}
</script>

<style>
	page {
		background: linear-gradient(180deg, #FF7E32 -1.61%, #F4F6F8 43.79%);

	}
	
	view {
		box-sizing: border-box;
	}
</style>
