<template>
	<view>
		<view class="" style="padding: 30rpx;">
			<view class="" style="display: flex;align-items: center;padding: 0 24rpx;">
				<image src="/static/images/rgicon.png" style="width: 48rpx;height: 48rpx;" mode=""></image>
				<text style="color: #222222;font-size: 36rpx;margin-left: 8rpx;">{{detail.shopName}}</text>
				<!-- <view style="font-size:28rpx;color: #0095F2;display: flex;align-items: center;margin-left: 20rpx;" @click="toMap">点击导航</view> -->
			</view>
			<view class="">
				<view class="" style="display: flex;align-items: center;margin: 20rpx;padding-left: 64rpx;width: 100%;">
					<text style="font-size: 26rpx;color: #999999;">营业时间:</text>
					<text style="font-size: 26rpx;color: #222222;">{{detail.startTime}} - {{detail.endTime}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin: 20rpx;padding-left: 64rpx;width: 100%;">
					<text style="font-size: 26rpx;color: #999999;">地址:</text>
					<text style="font-size: 26rpx;color: #222222;">{{detail.address}}</text>
				</view>
				<!-- <view class="" style="display: flex;align-items: center;padding-left: 70rpx;width: 100%;">
					<view class="" v-for="item in detail.equipmentList" style="width: fit-content;padding: 9rpx 12rpx;border-radius: 8rpx;background-color: #EFEFEF;
					display: flex;align-items: center;justify-content: center;font-size: 22rpx;color: #999999;margin-right: 20rpx;">
						{{item.serviceName}}
					</view>
				</view> -->
			</view>
			<view class=""
				style="width: 100%;height: 144rpx;border-radius: 12rpx;margin: 20rpx 0;display: flex;align-items: center;padding:0 20rpx;">
				<image :src="detail.pic" style="width: 32%;height: 144rpx;border-radius: 12rpx;margin-right: 12rpx;"
					mode=""></image>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">我的车辆</p>
					<view class="" @click="addCar"
						style="color: #999999;font-size: 24rpx;display: flex;align-items: center;">
						<text>车辆管理</text>
						<uv-icon name="arrow-right" size="14" color="#999999"></uv-icon>
					</view>
				</view>
				<p style="color: #FF7E32;font-size: 24rpx;margin-top: 10rpx;">部分站点免费停车，无牌车无法减免</p>
				<view class="" style="display: flex;align-items: center;margin-top: 10rpx;flex-wrap: wrap;">
					<view class="" v-for="item in carList" @click="chooseCar(item)"
						:style="current==item.id?'background:#C8FFEF;color:#077E6C;':'background:#EFEFEF;color:#999999;'"
						style="width: fit-content;padding: 10rpx;border-radius: 12rpx;height: 64rpx;padding: 12rpx 18rpx;margin-right: 12rpx;">
						<text>{{item.carNumber}}</text>
					</view>
					<view class="" @click="addCar"
						style="height: 64rpx;width: 64rpx;border-radius: 12rpx;background:#EFEFEF;color:#999999;display: flex;align-items: center;justify-content: center;">
						<uv-icon name="plus" size="16" color="#999999"></uv-icon>

					</view>
				</view>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">洗车套餐</p>
				</view>
				<view class=""
					style="margin-top: 30rpx;display: flex;align-items: center;justify-content: space-between;border-bottom:1px solid #e4e7ed;height: 146rpx;"
					@click="chooseModey(item)" v-for="item in serverList">
					<view class="">
						<view class="" style="display: flex;align-items: center;">
							<image src="/static/images/kuai.png" style="height: 38rpx;width: 60rpx;"
								v-if="item.name.includes('快')" mode=""></image>
							<image src="/static/images/you.png" style="height: 38rpx;width: 60rpx;" v-else mode="">
							</image>
							<text style="font-size: 28rpx;font-weight: 600;margin-left: 10rpx;">{{item.name}}</text>
							<text
								style="font-size:38rpx;font-weight: 600;margin-left: 10rpx;color: #222222;">{{item.price}}</text>
							<text style="font-size: 24rpx;color: #222222;">元/次</text>
						</view>
					</view>
					<view class=""
						style="display: flex;align-items: center;justify-content: center;width: 60rpx;height: 60rpx;">
						<view class="" v-if="serverId!=item.id"
							style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

							<uv-icon name="checkmark" color="#999999" size="16"></uv-icon>
						</view>
						<view class="" v-if="serverId==item.id" :style="serverId==item.id?'background:#C8FFEF;':''"
							style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

							<uv-icon name="checkmark" color="#077E6C" size="16"></uv-icon>
						</view>
					</view>
				</view>
			</view>
			<!-- 优惠券 -->
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 100rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;display: flex;align-items: center;justify-content: space-between;"
				@click="chooseCoupon">
				<text>优惠券</text>
				<view class="" style="display: flex;align-items: center;" v-if="!coupon">
					<text style="color: #FF7E32;" v-if="couponList.length>0">{{couponList.length}}张优惠券可用</text>
					<text style="color: #FF7E32;" v-else>暂无优惠券</text>
					<uv-icon name="arrow-right"></uv-icon>
				</view>
				<view class="" v-else>
					<text style="color: #FF7E32;">已减{{coupon.amount}}元</text>
				</view>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">支付方式</p>
				</view>
				<view class=""
					style="margin-top: 30rpx;display: flex;align-items: center;justify-content: space-between;"
					@click="choosePay(item)" v-for="item in payList">
					<view class="">
						<view class="" style="display: flex;align-items: center;">
							<image :src="item.icon" style="width: 32rpx;height: 32rpx;" mode=""></image>
							<text style="font-size: 28rpx;font-weight: 600;margin-left: 10rpx;">{{item.name}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;">
							<p style="font-size: 22rpx;color: #FF7E32;margin-left: 46rpx;margin-top: 10rpx;">
								{{item.type=='vip'?'会员余额剩余'+centerUser.userBalance.balance+'元':''}}
							</p>
							<p v-if="item.type=='vip'"
								style="font-size: 22rpx;color: #FF7E32;margin-left: 46rpx;margin-top: 10rpx;"
								@click="toCharge">去充值</p>
						</view>

					</view>
					<view class=""
						style="display: flex;align-items: center;justify-content: center;width: 60rpx;height: 60rpx;">
						<view class="" v-if="payItem!=item.id"
							style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

							<uv-icon name="checkmark" color="#999999" size="16"></uv-icon>
						</view>
						<view class="" v-if="payItem==item.id" :style="payItem==item.id?'background:#C8FFEF;':''"
							style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

							<uv-icon name="checkmark" color="#077E6C" size="16"></uv-icon>
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="" style="height: 160rpx;">
			
		</view>
		<view class=""
			style="position: fixed;bottom: 0;height: 148rpx;width: 100%;display: flex;align-items: center;justify-content: center;background-color: #FFFFFF;">
			<view class=""
				style="background: #00C7A3;width: 680rpx;height: 88rpx;border-radius: 88rpx;display: flex;align-items: center;justify-content: center;"
				@click="toPay">
				<text style="color: #FFFFFF;">确定支付</text>
			</view>
		</view>

		<!-- 优惠券弹窗 -->
		<uv-popup ref="popupCoupon" closeable round="30">
			<view style="height: 900rpx;background:#F4F6F8;padding: 30rpx;">
				<p style="margin: 0rpx auto;display: flex;justify-content: center;">选择优惠券</p>
				<scroll-view class="scrolls" scroll-y style="height: 870rpx;">
					<!-- colors:按钮颜色 couponList:优惠卷列表数据  @onReceive：领取或立即使用按钮事件 -->
					<m-coupon v-if="hackReset" :couponList="couponList" :isSelect="coupon" :shopId="shop_id"
						@chooseCoupon="choose"></m-coupon>

				</scroll-view>
			</view>
		</uv-popup>
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
		<!-- 支付弹窗 -->
		<uv-popup ref="popupPay" closeable round="30">
			<view style="background: #FFFFFF;height: 400rpx;background:##ffffff;padding: 30rpx;width: 700rpx;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
				<p style="font-size: 34rpx;color: #222222;">本次会员支付金额为:</p>
				<p style="font-size: 44rpx;">￥{{lastPrice}}</p>
				<view class="" style="display: flex;align-items: center;justify-content: space-around;width: 700rpx;margin-top: 60rpx;">
					<view class="" @click="canclePay" style="height: 80rpx;width: 280rpx;border-radius: 48rpx;background-color: #EFEFEF;color: #8e9093;display: flex;align-items: center;justify-content: center;">
						取消
					</view>
					<view class="" @click="surePay" style="height: 80rpx;width: 280rpx;border-radius: 48rpx;background-color: #439057;color: #FFFFFF;display: flex;align-items: center;justify-content: center;">
						确认支付
					</view>
				</view>
			</view>
		</uv-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				detail: null,
				carList: [],
				carNumber: '',
				current: 0,
				current1: 1,
				centerUser: '',
				params: {
					pageNumber: 1,
					pageSize: 10,
					parkId: '',
					fastDevice: true
				},
				list: [],
				payList: [{
					id: 1,
					type: 'weixin',
					name: '微信支付',
					icon: '../../../static/images/wxpay.png',
					desc: '充值消费后剩余金额将原路退回'
				}, {
					id: 2,
					type: 'vip',
					name: '会员支付',
					icon: '../../../static/images/pay.png',
					desc: ''
				}],
				payItem: 1,
				serverList: [],
				server: null,
				serverId: 1,
				coupon: '',
				couponList: [],
				hackReset: true,
				shop_id:'',
				if_login:false,
				
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'washList', 'wash']),
			lastPrice() {
				if (this.server) {
					if (this.coupon) {
						return this.server.price - this.coupon.amount
					} else {
						return this.server.price
					}
				}
			}
		},
		async onLoad(options){
			console.log(options,'kkl');
		},
		async onShow() {
			await this.$onLaunched;
			let scene = wx.getEnterOptionsSync()
			this.$nextTick(() => {
				this.hackReset = true
				console.log(this.userInfo, 'this.userInfo');
				if (this.userInfo.phone) {
					this.if_login = false
			
				} else {
					this.if_login = true
				}
			})
			if(this.wash){
				this.shop_id = this.wash.id
				this.detail = this.wash
				this.$iBox.http('getCarWashMode', {
					shopId: this.detail.id
				})({
					method: 'get'
				}).then(res => {
					this.serverList = res.data
					this.serverId = res.data[0].id
					this.server = res.data[0]
					this.$iBox.http('getUserCarWashCouponList', {
						pageNumber: 1,
						pageSize: 1000,
						userCouponStatus: 0,
						useCondition: this.server.price,
						shopId: this.detail.id
					})({
						method: 'post'
					}).then(res => {
						uni.hideLoading()
						let list = []
						res.data.list.forEach(item =>{
							item.shopList.forEach(item1 => {
								if(item1.shopId == this.shop_id){
									list.push(item)
								}
							})
						})
						
						this.couponList = list
				
					})
				})
			}else{
				if (scene.query.scene) {
					let query = decodeURIComponent(scene.query.scene)
					console.log(query,'query');
					//解析参数
					if (query.includes("m=") && query.includes("i=")) {
						//m=1洗车  m=2,停车  m=3停电
						this.shop_id = this.$iBox.linkFormat(query, "i")
						this.$iBox.http('getCarWashShop', {id:this.shop_id})({
							method: 'get'
						}).then(res => { 
							this.detail = res.data
							this.$iBox.http('getCarWashMode', {
								shopId: this.detail.id
							})({
								method: 'get'
							}).then(res => {
								this.serverList = res.data
								this.serverId = res.data[0].id
								this.server = res.data[0]
								this.$iBox.http('getUserCarWashCouponList', {
									pageNumber: 1,
									pageSize: 1000,
									userCouponStatus: 0,
									useCondition: this.server.price,
									shopId: this.detail.id
								})({
									method: 'post'
								}).then(res => {
									uni.hideLoading()
									let list = []
									res.data.list.forEach(item =>{
										item.shopList.forEach(item1 => {
											if(item1.shopId == this.shop_id){
												list.push(item)
											}
										})
									})
									
									this.couponList = list
							
								})
							})
						})
						
					} 
				
				}
			}
			
			
			
			
			this.bool = true

			this.$iBox.http('getUserInfo', {

			})({
				method: 'get'
			}).then(res => {
				let centerUser = res.data
				centerUser.token = this.userInfo.token
				centerUser.session_key = this.userInfo.session_key
				this.centerUser = centerUser
				this.updateUserInfo(this.centerUser)
				this.$iBox.http('getUserCar', {

				})({
					method: 'get'
				}).then(res => {
					if (res.data.length > 0) {
						this.carNumber = res.data[0].carNumber
						this.carList = res.data
						this.current = res.data[0].id
					}
				})

			})

			


		},
		methods: {
			...mapActions('login', ['pushNumber', 'updateUserInfo','pushWash']),
			chooseCar(e) {
				this.carNumber = e.carNumber
				this.current = e.id
			},
			chooseCoupon() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
				})
				this.$refs.popupCoupon.open('bottom');
			},
			loginSucess() {
			
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false
			
					} else {
						this.if_login = true
					}
				})
			},
			addCar() {
				uni.navigateTo({
					url: '/pages/addCar/addCar'
				})
			},
			chooseModey(e) {
				console.log(e,'l');
				this.server = e
				this.serverId = e.id
			},
			choose(e) {
				console.log('33');
				this.$refs.popupCoupon.close('bottom');
				this.coupon = e
			},
			toCharge() {
				uni.navigateTo({
					url: '/pages/recharge/recharge?type=1'
				})
			},
			choosePay(e) {
				this.payItem = e.id
			},
			toPay(e) {
				console.log(this.lastPrice, 'dd');
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false
						if (this.payItem==1) {
							this.$iBox.http('addCarWashCarOrder', {
								payType: this.payItem,
								shopId: this.shop_id,
								washModeId: this.serverId,
								carNumber: this.carNumber,
								couponId: this.coupon ? this.coupon.id : ''
							})({
								method: 'post'
							}).then(res => {
								if(res.data){
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.sign,
										success: (res) => {
											uni.showModal({
												title:'提示',
												content:'支付成功',
												confirmText:'查看订单',
												success:(res)=> {
													if(res.confirm){
														uni.reLaunch({
															url:'/pages/order/order?type=2'
														})
													}
												}
											})
											
										},
										fail: function(err) {
											console.log(err, ';dd');
											uni.hideLoading()
										}
									});
								}else{
									uni.showModal({
										title:'提示',
										content:'支付成功',
										confirmText:'查看订单',
										success:(res)=> {
											if(res.confirm){
												uni.reLaunch({
													url:'/pages/order/order?type=2'
												})
											}
										}
									})
								}
								
							}).catch(err => {
								uni.showToast({
									icon: 'error',
									title: err.message,
									duration:2000
								})
							})
						} else {
							this.$refs.popupPay.open()
							
						}
					} else {
						this.if_login = true
					}
				})
			

			},
			canclePay(){
				this.$refs.popupPay.close()
			},
			surePay(){
				this.$iBox.http('addCarWashCarOrder', {
					payType: this.payItem,
					shopId: this.shop_id,
					washModeId: this.serverId,
					carNumber: this.carNumber,
					couponId: this.coupon ? this.coupon.id : ''
				})({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title:'提示',
						content:'支付成功',
						confirmText:'查看订单',
						success:(res)=> {
							if(res.confirm){
								uni.reLaunch({
									url:'/pages/order/order?type=2'
								})
							}
						}
					})
				}).catch(err => {
				
					uni.showToast({
						icon: 'error',
						title: err.message,
						duration:2000
					})
				})
			}

		},
		onUnload() {
			this.pushWash('')
		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">

</style>