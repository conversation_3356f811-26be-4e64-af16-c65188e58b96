<template>
	<view class="recharge-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="nav-back" @click="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="navbar-title">充值中心</text>
				<view class="nav-placeholder"></view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content" :style="{ marginTop: navBarHeight + 'px' }">
			<!-- 充值选项卡 -->
			<view class="recharge-tabs">
				<view
					class="tab-item"
					:class="{ active: activeTab === 'cash' }"
					@click="switchTab('cash')"
				>
					<text class="tab-icon">💰</text>
					<text class="tab-text">现金充值</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'card' }"
					@click="switchTab('card')"
				>
					<text class="tab-icon">🎫</text>
					<text class="tab-text">洗车卡</text>
				</view>
			</view>

			<!-- 现金充值 -->
			<view class="cash-recharge" v-if="activeTab === 'cash'">
				<view class="balance-display">
					<view class="balance-info">
						<text class="balance-label">当前余额</text>
						<text class="balance-amount">¥{{userBalance}}</text>
					</view>
					<view class="balance-actions">
						<view class="refresh-btn" @click="refreshBalance">
							<text class="refresh-icon">🔄</text>
						</view>
						<view class="record-btn" @click="showBalanceRecord">
							<text class="record-icon">📋</text>
						</view>
					</view>
				</view>

				<view class="amount-options">
					<text class="section-title">选择充值金额</text>
					<view class="amount-grid">
						<view
							class="amount-item"
							:class="{ selected: selectedAmount === item.value }"
							v-for="item in cashAmounts"
							:key="item.id"
							@click="selectCashAmount(item)"
						>
							<text class="amount-value">¥{{item.value}}</text>
							<text class="amount-bonus" v-if="item.bonus > 0">送¥{{item.bonus}}</text>
							<text class="amount-desc" v-if="item.description && !item.bonus">{{item.description}}</text>
						</view>
					</view>
				</view>

				<view class="custom-amount">
					<text class="section-title">自定义金额</text>
					<view class="input-wrapper">
						<text class="currency">¥</text>
						<input 
							class="amount-input" 
							type="number" 
							placeholder="请输入金额"
							v-model="customAmount"
							@input="onCustomInput"
						/>
					</view>
				</view>
			</view>

			<!-- 洗车卡购买 -->
			<view class="card-purchase" v-if="activeTab === 'card'">
				<view class="card-display">
					<view class="balance-info">
						<text class="card-label">当前洗车卡</text>
						<text class="card-count">{{washCardCount}}张</text>
					</view>
					<view class="refresh-btn" @click="refreshBalance">
						<text class="refresh-icon">🔄</text>
					</view>
				</view>

				<view class="card-options">
					<text class="section-title">选择洗车卡套餐</text>
					<view class="card-list">
						<view
							class="card-item"
							:class="{ selected: selectedCard === item.id }"
							v-for="item in cardPackages"
							:key="item.id"
							@click="selectCard(item)"
						>
							<view class="card-info">
								<text class="card-name">{{item.name}}</text>
								<text class="card-desc">{{item.desc}}</text>
								<text class="card-price">¥{{item.price}}</text>
								<text class="card-times" v-if="item.count > 1">{{item.count}}次使用</text>
							</view>
							<view class="card-badge" v-if="item.tag">
								<text class="badge-text">{{item.tag}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 余额记录弹窗 -->
		<view class="balance-record-modal" v-if="showRecordModal" @click="closeBalanceRecord">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">余额记录</text>
					<view class="modal-close" @click="closeBalanceRecord">
						<text class="close-icon">✕</text>
					</view>
				</view>

				<scroll-view class="record-list" scroll-y="true">
					<view class="record-item" v-for="item in balanceRecords" :key="item.id">
						<view class="record-info">
							<text class="record-type">{{getRecordTypeText(item.type)}}</text>
							<text class="record-time">{{formatTime(item.createTime)}}</text>
							<text class="record-order" v-if="item.orderNo">订单号：{{item.orderNo}}</text>
						</view>
						<view class="record-amount">
							<text class="amount-text" :class="{ positive: item.amount > 0, negative: item.amount < 0 }">
								{{item.amount > 0 ? '+' : ''}}¥{{Math.abs(item.amount)}}
							</text>
							<text class="balance-text">余额：¥{{item.balance}}</text>
						</view>
					</view>

					<view class="no-more" v-if="balanceRecords.length === 0">
						<text class="no-more-text">暂无记录</text>
					</view>

					<view class="loading-more" v-if="loadingMore">
						<text class="loading-text">加载中...</text>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 底部支付按钮 -->
		<view class="pay-section">
			<view class="pay-info">
				<text class="pay-text">支付金额：¥{{payAmount}}</text>
			</view>
			<view class="pay-btn" :class="{ disabled: !canPay }" @click="handlePay">
				<text class="pay-btn-text">
					{{isPaymentProcessing ? '支付中...' : '立即支付'}}
				</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			navBarHeight: 0,
			userBalance: '0.00',
			washCardCount: 0,
			activeTab: 'cash',
			selectedAmount: 0,
			customAmount: '',
			selectedCard: 0,
			cashAmounts: [],
			isPaymentProcessing: false,
			cardPackages: [],
			shopId: 1, // 默认店铺ID
			// 余额记录相关
			showRecordModal: false,
			balanceRecords: [],
			loadingMore: false,
			recordPageNum: 1,
			recordPageSize: 10,
			hasMoreRecords: true
		};
	},
	
	computed: {
		// 支付金额
		payAmount() {
			if (this.activeTab === 'cash') {
				return this.selectedAmount || parseFloat(this.customAmount) || 0;
			} else {
				const selectedPackage = this.cardPackages.find(item => item.id === this.selectedCard);
				return selectedPackage ? selectedPackage.price : 0;
			}
		},
		
		// 是否可以支付
		canPay() {
			return this.payAmount > 0 && !this.isPaymentProcessing;
		}
	},
	
	onLoad(options) {
		this.initPage();

		// 从页面参数获取shopId
		if (options && options.shopId) {
			this.shopId = parseInt(options.shopId);
		}

		this.loadUserData();
	},

	onShow() {
		// 页面显示时刷新数据
		this.loadUserBalance();
	},
	
	methods: {
		initPage() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight;
			this.navBarHeight = this.statusBarHeight + 44;
		},
		
		loadUserData() {
			// 调用接口获取用户数据
			this.loadUserBalance();
			this.loadChargeSettings();
			this.loadWashCardPackages();
		},

		// 获取充值设置
		loadChargeSettings() {
			this.$iBox.http('getUserChargeSetting', {
				pageNumber: 1,
				pageSize: 10
			})({
				method: 'post'
			}).then(res => {
				if (res.data && res.data.list) {
					this.cashAmounts = res.data.list.map(item => ({
						id: item.id,
						value: item.rechargeAmount,
						bonus: item.giveAmount || 0,
						description: item.description || ''
					}));
				}
			}).catch(err => {
				console.log('获取充值设置失败', err);
				// 使用默认充值选项
				this.cashAmounts = [
					{ id: 1, value: 50, bonus: 0, description: '' },
					{ id: 2, value: 100, bonus: 5, description: '送5元' },
					{ id: 3, value: 200, bonus: 15, description: '送15元' },
					{ id: 4, value: 500, bonus: 50, description: '送50元' },
					{ id: 5, value: 1000, bonus: 120, description: '送120元' },
					{ id: 6, value: 2000, bonus: 300, description: '送300元' }
				];
			});
		},

		// 获取洗车卡套餐列表
		// 注意：这里使用getUserOnceWashCardList接口获取可购买的洗车卡套餐
		loadWashCardPackages() {
			this.$iBox.http('getUserOnceWashCardList', {
				pageNumber: 1,
				pageSize: 20 // 获取更多套餐选项
			})({
				method: 'post'
			}).then(res => {
				if (res.data && res.data.list) {
					this.cardPackages = res.data.list.map(item => ({
						id: item.id,
						name: item.name || item.cardName || '洗车卡',
						desc: item.description || item.desc || `${item.times || 1}次洗车服务`,
						price: item.price || item.amount || 0,
						count: item.times || item.count || 1,
						tag: item.tag || (item.isRecommend ? '推荐' : ''),
						shopId: this.shopId,
						// 保留原始数据，以备后用
						originalData: item
					}));
				}
			}).catch(err => {
				console.log('获取洗车卡套餐失败', err);
				// 如果接口失败，使用默认套餐
				this.cardPackages = [
					{
						id: 1,
						name: '基础洗车卡',
						desc: '5次洗车服务',
						price: 100,
						count: 5,
						tag: '',
						shopId: this.shopId
					},
					{
						id: 2,
						name: '超值洗车卡',
						desc: '10次洗车服务',
						price: 180,
						count: 10,
						tag: '推荐',
						shopId: this.shopId
					},
					{
						id: 3,
						name: '豪华洗车卡',
						desc: '20次洗车服务',
						price: 320,
						count: 20,
						tag: '最划算',
						shopId: this.shopId
					}
				];
			});
		},

		// 获取用户已购买的洗车卡（用于显示数量）
		getUserWashCards() {
			return this.$iBox.http('getUserOnceWashCardList', {
				pageNumber: 1,
				pageSize: 10,
				status: 0 // 可能需要传递状态参数来区分已购买的卡
			})({
				method: 'post'
			}).then(res => {
				// 这里可能需要根据实际接口返回的数据结构进行调整
				// 如果接口返回的是套餐列表，需要另外的接口获取用户已购买的卡
				return res.data.list || [];
			});
		},

		// 加载用户余额
		loadUserBalance() {
			Promise.all([
				this.$iBox.http('getCarWashSelfBalance', {})({
					method: 'get'
				}),
				this.getUserWashCards()
			]).then(([balanceRes, washCards]) => {
				this.userBalance = balanceRes.data.balance || '0.00';
				this.washCardCount = washCards ? washCards.length : 0;
			}).catch(err => {
				console.log('加载用户余额失败', err);
				// 如果接口失败，使用默认值
				this.userBalance = '0.00';
				this.washCardCount = 0;
			});
		},
		
		goBack() {
			uni.navigateBack();
		},

		// 刷新余额
		refreshBalance() {
			this.loadUserBalance();
			uni.showToast({
				title: '数据已刷新',
				icon: 'success'
			});
		},

		// 显示余额记录
		showBalanceRecord() {
			this.showRecordModal = true;
			this.loadBalanceRecords();
		},

		// 关闭余额记录
		closeBalanceRecord() {
			this.showRecordModal = false;
			this.balanceRecords = [];
			this.recordPageNum = 1;
			this.hasMoreRecords = true;
		},

		// 加载余额记录
		loadBalanceRecords(loadMore = false) {
			if (this.loadingMore) return;

			this.loadingMore = true;

			this.$iBox.http('getCarWashSelfBalanceRecord', {
				pageNumber: this.recordPageNum,
				pageSize: this.recordPageSize
			})({
				method: 'post'
			}).then(res => {
				this.loadingMore = false;

				if (res.data && res.data.list) {
					if (loadMore) {
						this.balanceRecords = [...this.balanceRecords, ...res.data.list];
					} else {
						this.balanceRecords = res.data.list;
					}

					// 判断是否还有更多数据
					this.hasMoreRecords = res.data.hasNextPage || false;

					if (this.hasMoreRecords) {
						this.recordPageNum++;
					}
				}
			}).catch(err => {
				this.loadingMore = false;
				console.log('获取余额记录失败', err);
				uni.showToast({
					title: '获取记录失败',
					icon: 'none'
				});
			});
		},

		// 获取记录类型文本
		getRecordTypeText(type) {
			const typeMap = {
				1: '充值',
				2: '消费',
				3: '退款',
				4: '赠送',
				5: '扣除'
			};
			return typeMap[type] || '其他';
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const now = new Date();
			const diff = now - date;
			const oneDay = 24 * 60 * 60 * 1000;

			if (diff < oneDay) {
				return date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit'
				});
			} else {
				return date.toLocaleDateString('zh-CN', {
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			}
		},
		
		// 切换充值选项卡
		switchTab(tab) {
			this.activeTab = tab;
			this.resetRechargeData();
		},
		
		// 选择现金充值金额
		selectCashAmount(item) {
			this.selectedAmount = item.value;
			this.customAmount = '';
		},
		
		// 自定义金额输入
		onCustomInput() {
			this.selectedAmount = 0;
		},
		
		// 选择洗车卡套餐
		selectCard(item) {
			this.selectedCard = item.id;
		},
		
		// 重置充值数据
		resetRechargeData() {
			this.selectedAmount = 0;
			this.customAmount = '';
			this.selectedCard = 0;
		},
		
		// 处理支付
		handlePay() {
			if (!this.canPay) return;

			const paymentData = {
				type: this.activeTab,
				amount: this.payAmount,
				...(this.activeTab === 'cash' ? {
					cashAmount: this.selectedAmount || parseFloat(this.customAmount)
				} : {
					cardPackage: this.cardPackages.find(item => item.id === this.selectedCard)
				})
			};

			// 显示支付确认对话框
			const actionText = this.activeTab === 'cash' ? '充值到余额' : '购买洗车卡';
			const bonusText = this.activeTab === 'cash' && this.selectedAmount > 0 ?
				(() => {
					const selectedItem = this.cashAmounts.find(item => item.value === this.selectedAmount);
					return selectedItem && selectedItem.bonus > 0 ? `（含赠送¥${selectedItem.bonus}）` : '';
				})() : '';

			uni.showModal({
				title: '确认支付',
				content: `确认支付¥${this.payAmount}${actionText}${bonusText}？`,
				confirmText: '确认支付',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.processPayment(paymentData);
					}
				}
			});
		},
		
		// 处理支付流程
		processPayment(paymentData) {
			this.isPaymentProcessing = true;

			uni.showLoading({
				title: '创建订单中...'
			});

			// 根据支付类型调用不同的接口
			let paymentPromise;

			if (paymentData.type === 'cash') {
				// 现金充值接口
				const selectedSetting = this.cashAmounts.find(item => item.value === paymentData.cashAmount);
				paymentPromise = this.$iBox.http('rechargeBalance', {
					settingId: selectedSetting ? selectedSetting.id : null,
					amount: paymentData.cashAmount
				})({
					method: 'post'
				});
			} else {
				// 洗车卡购买接口
				paymentPromise = this.$iBox.http('buyOnceWashCard', {
					shopId: this.shopId,
					cardId: paymentData.cardPackage.id
				})({
					method: 'post'
				});
			}

			paymentPromise.then(res => {
				uni.hideLoading();

				// 调用微信支付
				this.callWechatPay(res.data, paymentData);

			}).catch(err => {
				uni.hideLoading();
				this.isPaymentProcessing = false;
				console.log('创建订单失败', err);

				uni.showToast({
					title: '创建订单失败，请重试',
					icon: 'none'
				});
			});
		},

		// 调用微信支付
		callWechatPay(paymentInfo, paymentData) {
			uni.showLoading({
				title: '调起支付中...'
			});

			// 微信支付
			uni.requestPayment({
				provider: 'wxpay',
				timeStamp: paymentInfo.timeStamp,
				nonceStr: paymentInfo.nonceStr,
				package: paymentInfo.package,
				signType: 'MD5',
				paySign: paymentInfo.sign,
				success: (res) => {
					console.log('支付成功', res);
					uni.hideLoading();
					this.isPaymentProcessing = false;

					// 支付成功处理
					this.handlePaymentSuccess(paymentData);
				},
				fail: (err) => {
					console.log('支付失败', err);
					uni.hideLoading();
					this.isPaymentProcessing = false;

					// 判断是用户取消还是支付失败
					if (err.errMsg && err.errMsg.includes('cancel')) {
						uni.showToast({
							title: '支付已取消',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: '支付失败，请重试',
							icon: 'none'
						});
					}
				}
			});
		},

		// 处理支付成功
		handlePaymentSuccess(paymentData) {
			if (paymentData.type === 'cash') {
				uni.showToast({
					title: '充值成功',
					icon: 'success'
				});
			} else {
				uni.showToast({
					title: '购买成功',
					icon: 'success'
				});
			}

			// 重新加载用户数据
			this.loadUserBalance();

			// 重置选择数据
			this.resetRechargeData();

			// 跳转到结果页面
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/resultPage/resultPage'
				});
			}, 1500);
		}
	}
};
</script>

<style lang="scss" scoped>
.recharge-page {
	min-height: 100vh;
	background: linear-gradient(180deg, #F8F9FA 0%, #FFFFFF 100%);
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 2rpx 16rpx rgba(102, 126, 234, 0.2);

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.nav-back {
			width: 64rpx;
			height: 64rpx;
			border-radius: 32rpx;
			background: rgba(255, 255, 255, 0.2);
			display: flex;
			align-items: center;
			justify-content: center;

			.back-icon {
				font-size: 32rpx;
				color: #FFFFFF;
				font-weight: 600;
			}

			&:active {
				transform: scale(0.95);
			}
		}

		.navbar-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #FFFFFF;
			letter-spacing: 1rpx;
		}

		.nav-placeholder {
			width: 64rpx;
		}
	}
}

.page-content {
	padding: 32rpx;
	padding-bottom: 200rpx;
}

.recharge-tabs {
	display: flex;
	background: linear-gradient(135deg, #F8F9FA 0%, #F0F2F5 100%);
	border-radius: 20rpx;
	padding: 6rpx;
	margin-bottom: 32rpx;
	border: 2rpx solid #E8EAED;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: 4rpx;
		left: 50%;
		width: 40rpx;
		height: 4rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 2rpx;
		transform: translateX(-50%);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		opacity: 0.6;
	}

	.tab-item {
		flex: 1;
		padding: 20rpx 16rpx;
		text-align: center;
		border-radius: 16rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		cursor: pointer;

		&:hover:not(.active) {
			background: rgba(102, 126, 234, 0.05);
			transform: translateY(-1rpx);

			.tab-text {
				color: #667eea;
			}
		}

		&.active {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
			transform: translateY(-3rpx);

			&::before {
				content: '';
				position: absolute;
				top: -3rpx;
				left: -3rpx;
				right: -3rpx;
				bottom: -3rpx;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				border-radius: 19rpx;
				z-index: -1;
				opacity: 0.3;
				animation: pulse 2s infinite;
			}

			.tab-text {
				color: #FFFFFF;
				font-weight: 700;
				text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
				transform: scale(1.05);
			}

			.tab-icon {
				color: #FFFFFF;
				opacity: 1;
				transform: scale(1.1);
				filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
			}
		}

		&:active {
			transform: scale(0.98);
		}

		.tab-text {
			font-size: 30rpx;
			color: #666666;
			font-weight: 500;
			transition: all 0.3s ease;
			display: block;
		}

		.tab-icon {
			font-size: 24rpx;
			color: #999999;
			margin-bottom: 4rpx;
			transition: all 0.3s ease;
			display: block;
		}
	}
}

.cash-recharge,
.card-purchase {
	.balance-display,
	.card-display {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: -50%;
			left: -50%;
			width: 200%;
			height: 200%;
			background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
			animation: shimmer 4s ease-in-out infinite;
		}

		.balance-info {
			display: flex;
			flex-direction: column;
			flex: 1;
		}

		.balance-label,
		.card-label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.9);
			margin-bottom: 8rpx;
		}

		.balance-amount,
		.card-count {
			font-size: 36rpx;
			font-weight: 600;
			color: #FFFFFF;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		}

		.balance-actions {
			display: flex;
			gap: 16rpx;
		}

		.refresh-btn,
		.record-btn {
			width: 48rpx;
			height: 48rpx;
			border-radius: 24rpx;
			background: rgba(255, 255, 255, 0.2);
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s ease;

			.refresh-icon,
			.record-icon {
				font-size: 20rpx;
			}

			&:active {
				transform: scale(0.9);
				background: rgba(255, 255, 255, 0.3);
			}
		}

		.refresh-btn:active {
			transform: scale(0.9) rotate(180deg);
		}
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 24rpx;
	}
}

.amount-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
	margin-bottom: 32rpx;

	.amount-item {
		background: #F8F9FA;
		border-radius: 12rpx;
		padding: 24rpx 16rpx;
		text-align: center;
		border: 2rpx solid #E5E5E5;
		transition: all 0.2s ease;
		position: relative;

		&:hover {
			border-color: #CCCCCC;
			transform: translateY(-2rpx);
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		}

		&.selected {
			background: rgba(102, 126, 234, 0.1);
			border-color: #667eea;
			box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
			transform: translateY(-2rpx);
		}

		&:active {
			transform: scale(0.98);
		}

		.amount-value {
			font-size: 28rpx;
			font-weight: 600;
			color: #333333;
			margin-bottom: 8rpx;
		}

		.amount-bonus {
			font-size: 20rpx;
			color: #FF7E32;
			background: rgba(255, 126, 50, 0.1);
			padding: 4rpx 8rpx;
			border-radius: 8rpx;
		}

		.amount-desc {
			font-size: 20rpx;
			color: #999999;
			margin-top: 4rpx;
		}
	}
}

.input-wrapper {
	background: #F8F9FA;
	border-radius: 12rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	border: 2rpx solid #E5E5E5;
	margin-bottom: 32rpx;
	transition: all 0.2s ease;

	&:hover {
		border-color: #CCCCCC;
	}

	&:focus-within {
		border-color: #667eea;
		background: rgba(102, 126, 234, 0.05);
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	.currency {
		font-size: 28rpx;
		color: #333333;
		font-weight: 600;
		margin-right: 16rpx;
	}

	.amount-input {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
		border: none;
		outline: none;
		background: transparent;
	}
}

.card-list {
	.card-item {
		background: #F8F9FA;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		border: 2rpx solid #E5E5E5;
		transition: all 0.2s ease;
		position: relative;

		&:hover {
			border-color: #CCCCCC;
			transform: translateY(-2rpx);
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		}

		&.selected {
			background: rgba(102, 126, 234, 0.1);
			border-color: #667eea;
			box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
			transform: translateY(-2rpx);
		}

		&:active {
			transform: scale(0.98);
		}

		.card-info {
			.card-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
				margin-bottom: 8rpx;
			}

			.card-desc {
				font-size: 24rpx;
				color: #666666;
				margin-bottom: 12rpx;
			}

			.card-price {
				font-size: 36rpx;
				font-weight: 600;
				color: #667eea;
			}

			.card-times {
				font-size: 22rpx;
				color: #999999;
				margin-top: 4rpx;
			}
		}

		.card-badge {
			position: absolute;
			top: -8rpx;
			right: -8rpx;
			background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
			padding: 6rpx 12rpx;
			border-radius: 12rpx;

			.badge-text {
				font-size: 20rpx;
				color: #FFFFFF;
				font-weight: 600;
			}
		}
	}
}

.pay-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #FFFFFF;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #F0F0F0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);

	.pay-info {
		.pay-text {
			font-size: 28rpx;
			color: #333333;
			font-weight: 600;
		}
	}

	.pay-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		transition: all 0.2s ease;

		&.disabled {
			background: #CCCCCC;
			opacity: 0.6;
		}

		&:active:not(.disabled) {
			transform: scale(0.98);
		}

		.pay-btn-text {
			font-size: 28rpx;
			color: #FFFFFF;
			font-weight: 600;
		}
	}
}

@keyframes pulse {
	0% {
		opacity: 0.3;
		transform: scale(1);
	}
	50% {
		opacity: 0.5;
		transform: scale(1.02);
	}
	100% {
		opacity: 0.3;
		transform: scale(1);
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	50% {
		transform: translateX(100%) translateY(100%) rotate(45deg);
	}
	100% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
}

// 余额记录弹窗样式
.balance-record-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 3000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 32rpx;

	.modal-content {
		background: #FFFFFF;
		border-radius: 20rpx;
		width: 100%;
		max-width: 600rpx;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.modal-header {
			padding: 32rpx;
			border-bottom: 1rpx solid #F0F0F0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.modal-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
			}

			.modal-close {
				width: 48rpx;
				height: 48rpx;
				border-radius: 24rpx;
				background: #F5F5F5;
				display: flex;
				align-items: center;
				justify-content: center;

				.close-icon {
					font-size: 24rpx;
					color: #666666;
				}

				&:active {
					transform: scale(0.9);
					background: #E5E5E5;
				}
			}
		}

		.record-list {
			flex: 1;
			padding: 16rpx 32rpx 32rpx;

			.record-item {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				padding: 24rpx 0;
				border-bottom: 1rpx solid #F8F8F8;

				&:last-child {
					border-bottom: none;
				}

				.record-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					gap: 8rpx;

					.record-type {
						font-size: 28rpx;
						font-weight: 600;
						color: #333333;
					}

					.record-time {
						font-size: 24rpx;
						color: #999999;
					}

					.record-order {
						font-size: 22rpx;
						color: #CCCCCC;
					}
				}

				.record-amount {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					gap: 8rpx;

					.amount-text {
						font-size: 28rpx;
						font-weight: 600;

						&.positive {
							color: #52C41A;
						}

						&.negative {
							color: #FF4D4F;
						}
					}

					.balance-text {
						font-size: 22rpx;
						color: #999999;
					}
				}
			}

			.no-more,
			.loading-more {
				text-align: center;
				padding: 32rpx;

				.no-more-text,
				.loading-text {
					font-size: 24rpx;
					color: #CCCCCC;
				}
			}
		}
	}
}
</style>
