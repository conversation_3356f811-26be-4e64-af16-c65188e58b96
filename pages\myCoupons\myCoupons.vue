<template>
	<view>
		<uv-sticky bgColor="#fff">
			<uv-tabs :list="list" @click="click" :scrollable='false' lineColor="#ffb800" lineWidth="40"></uv-tabs>
			<uv-subsection :list="list1" :current="current1" @change="change" activeColor="#00C7A3"></uv-subsection>
		</uv-sticky>

		<scroll-view class="scrolls" scroll-y style="height: 95vh;">
			<!-- colors:按钮颜色 couponList:优惠卷列表数据  @onReceive：领取或立即使用按钮事件 -->
			<m-couponCenter v-if="hackReset&&couponList.length>0" :couponList="couponList" type="my"></m-couponCenter>
			<uv-empty v-else mode="coupon" icon="/static/images/yhq.png" width="60" height="50"
				marginTop="80"></uv-empty>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list: [],
				couponList: [],
				params: {
					pageNumber: 1,
					pageSize: 10,
					userCouponStatus:''
				},
				hackReset: true,
				current: 0,
				list1: ['未使用', '已过期'],
				current1: 0
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'washList', 'wash'])
		},
		async onLoad() {
			await this.$onLaunched;
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})

			let scene = wx.getEnterOptionsSync()


			if (scene.query.scene) {
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				console.log(scene, this.shop, query, 'query');
				if (query.includes("ma=") && query.includes("c=")) {
					let mid = this.$iBox.linkFormat(query, "ma")
					let cid = this.$iBox.linkFormat(query, "c")
					let time = this.$iBox.linkFormat(query, "ti")
					let timeNow = this.$moment().unix()
					if (timeNow - time > 60) {
						uni.showModal({
							title: '提示',
							content: '此二维码已过期，请联系客服重新生成二维码!',
							showCancel: false
						})
					} else {
						this.$iBox.http('takeCarWashCoupon', {
							couponId: cid,
							managerId: mid,
							time: time
						})({
							method: 'post'
						}).then(res => {
							uni.showModal({
								title: '提示',
								content: '您已成功领取一张优惠券!',
								showCancel: false
							})

						})
					}
					console.log(timeNow);

				}

			}
			
			this.$iBox.http('getCouponType', this.params)({
				method: 'get'
			}).then(res => {
				let list = []
				res.data.forEach(item => {
					let a = {
						name: ''
					}
					a.name = item.couponTypeName
					list.push(a)
				})
				this.list = list
			})
			this.params.userCouponStatus = 0
			this.params.pageNumber = 1
			this.getWashCouponList()
		},
		methods: {
			click(e) {
				console.log(e);
				this.current = e.index
				if (e.index == 0) {
					this.params.pageNumber = 1
					this.getWashCouponList()
				} else if (e.index == 1) {
					this.couponList = []
				} else {
					this.couponList = []
				}
			},
			change(index) {
				this.current1 = index;
				if(index==1){
					this.params.userCouponStatus = 2
				}else{
					this.params.userCouponStatus = 0
				}
				
				this.params.pageNumber = 1
				this.getWashCouponList()
			},
			getWashCouponList() {
				this.$iBox.http('getUserCarWashCouponList', this.params)({
					method: 'post'
				}).then(res => {
					this.couponList = res.data.list
				})
			}
		}
	}
</script>

<style>

</style>