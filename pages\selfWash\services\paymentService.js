// Payment service for selfWash functionality
class PaymentService {
  constructor(context) {
    this.context = context;
    this.cache = new Map();
    this.cacheTimeout = 30000; // 30 seconds
  }
  
  // Get cached data or fetch new data
  async getCachedData(key, fetchFn) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    
    try {
      const data = await fetchFn();
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });
      return data;
    } catch (error) {
      // Return cached data if available, even if expired
      if (cached) {
        return cached.data;
      }
      throw error;
    }
  }
  
  // Get user payment info (balance + wash cards)
  async getUserPaymentInfo() {
    return await this.getCachedData('userPaymentInfo', async () => {
      const [balanceRes, washCardsRes] = await Promise.all([
        this.context.$iBox.http('getCarWashSelfBalance', {})({ method: 'get' }),
        this.context.$iBox.http('getUserOnceWashCardRecordList', {
          pageNumber: 1,
          pageSize: 10,
          status: 0
        })({ method: 'post' })
      ]);
      
      return {
        balance: parseFloat(balanceRes.data.balance || 0),
        washCards: washCardsRes.data.list || []
      };
    });
  }
  
  // Check if user can pay
  async canUserPay() {
    try {
      const paymentInfo = await this.getUserPaymentInfo();
      return paymentInfo.balance > 0 || paymentInfo.washCards.length > 0;
    } catch (error) {
      console.error('Failed to check payment capability:', error);
      return false;
    }
  }
  
  // Get available payment options
  async getPaymentOptions() {
    const paymentInfo = await this.getUserPaymentInfo();
    const options = [];
    
    if (paymentInfo.balance > 0) {
      options.push({
        title: '余额支付',
        desc: `可用余额 ¥${paymentInfo.balance.toFixed(2)}`,
        icon: 'wallet',
        color: '#00C7A3',
        value: 'balance',
        payType: 1
      });
    }
    
    if (paymentInfo.washCards.length > 0) {
      options.push({
        title: '洗车卡支付',
        desc: `${paymentInfo.washCards.length}张洗车卡可用`,
        icon: 'card',
        color: '#FF7E32',
        value: 'washcard',
        payType: 3
      });
    }
    
    return options;
  }
  
  // Clear cache
  clearCache() {
    this.cache.clear();
  }
  
  // Refresh payment info
  async refreshPaymentInfo() {
    this.cache.delete('userPaymentInfo');
    return await this.getUserPaymentInfo();
  }
}

export default PaymentService;