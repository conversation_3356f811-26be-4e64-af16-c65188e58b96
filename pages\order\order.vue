<template>
	<view>
		<view class=""
			style="width: 100%;display: flex;align-items: center;justify-content: center;height: 180rpx;position: fixed;top: 0;background-color: #FFFFFF;flex-direction: column;">
			<view class=""
				style="display: flex;align-items: center;width: 100%;align-items: center;justify-content: center;margin-top: 30rpx;">
				<view class="" @click="chooseType(index)" v-for="(item, index) in list"
					style="height: 60rpx;width: 300rpx;display: flex;align-items: center;justify-content: center;"
					:style="index==current?'background:#C8FFEF;color:#077E6C;':'background:#EFEFEF;color:#999999;'">
					{{item}}
				</view>
			</view>

			<view class="" style="margin-top: 20rpx;">
				<uv-tabs :list="listTab" @click="chooseTab" :itemStyle="{width:'220rpx',height:'44px'}"
					:activeStyle="{color:'#077E6C'}"></uv-tabs>
			</view>
		</view>
		<view class="" style="height: 180rpx;">

		</view>
		<view class="" v-if="current==0">
			<view class="" v-for="item in chargeOrderList" v-if="chargeOrderList.length>0" @click="toPowerDetail(item)"
				style="margin: 20rpx auto; min-height: 580rpx;width: 702rpx;background-color: #FFFFFF;position: relative;">
				<view class="" style="height: 72rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;
				padding: 0 30rpx;background-color: #FFF6ED;font-size: 24rpx;color: #FF7E32;">
					<view class="">
						<text>充电订单</text>
						<text style="margin-left: 8rpx;">{{item.orderNo}}</text>
					</view>
					<text>{{item.chargeStatus==4?'已完成':'充电中'}}</text>
				</view>
				<view class="" style="min-height: 498rpx;width: 100%;padding: 30rpx;">
					<view class="" style="font-size: 32rpx;font-weight: 600;color: #222222;">
						{{item.stationName}}
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">设备名称:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.deviceName?item.deviceName:'暂无'}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">开始时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.startTime}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">结束时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.endTime}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">充电模式:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.chargeModel==1?'慢充':'快充'}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">车牌号码:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.plateNo}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">充电时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.timeLen}}分钟</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">充电度数:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.power?item.power:'暂无'}}度</text>
					</view>
					<view class="" style="width: 702rpx;height: 1px;background-color: #EFEFEF;margin: 10rpx auto;">

					</view>
					<view class="" style="display: flex;align-items: center;margin-top: 14rpx;">
						<view class="" style="display: flex;align-items: center;">
							<text style="font-size: 26rpx;color: #222222;">应收费用</text>
							<text style="font-size: 26rpx;color: #222222;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{(item.totalMoney+item.discounts).toFixed(2)}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
							<text style="font-size: 26rpx;color: #333333;">已优惠</text>
							<text style="font-size: 26rpx;color: #333333;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.discounts?item.discounts:0}}</text>
							<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
								<text style="font-size: 26rpx;color: #333333;">实收费用</text>
								<text style="font-size: 26rpx;color: #333333;">￥</text>
								<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.totalMoney?item.totalMoney:0}}</text>
							</view>
						</view>
					</view>
					
					
					<view class="" style="display:flex;align-items: center;">
						
						<view class="" style="display: flex;align-items: center;">
							<text style="font-size: 20rpx;color: #333333;">应收服务费</text>
							<text style="font-size: 26rpx;color: #333333;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{(item.seviceMoney+item.discounts).toFixed(2)}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
							<text style="font-size: 20rpx;color: #333333;">应收电费</text>
							<text style="font-size: 26rpx;color: #333333;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.elecMoney}}</text>
						</view>
					</view>
					<view class="" style="font-size: 22rpx;color: #999999;height: 50rpx;margin-top: 10rpx;height: 50rpx;display: flex;align-items: flex-end;" @click.stop="toPowerQuestion">
						<text style="text-align: center;">订单反馈</text>
						<uv-icon name="arrow-right" size="10" color="#999999"></uv-icon>
					</view>
					<view class="" @click.stop="toInvoice(item)" v-if="item.chargeStatus==4&&!item.userInvoiceApply.id"
						style="position: absolute;right: 40rpx;bottom: 10rpx;width: 144rpx;height: 61rpx;background-color: #FFF6ED;display: flex;align-items: center;justify-content: center;border-radius: 60rpx;">
						<text style="color: #FF7E32;font-size: 28rpx;">开发票</text>
					</view>
					<view class="" v-if="item.userInvoiceApply.id" @click.stop="lookInvice1(item)" style="position: absolute;right: 40rpx;bottom: 10rpx;width: fit-content;height: 61rpx;background-color: #FFF6ED;display: flex;align-items: center;justify-content: center;border-radius: 60rpx;padding: 0 20rpx;">
						<text style="color: #FF7E32;font-size: 28rpx;">查看发票</text>
						
					</view>
				</view>
			</view>
			<view class="" style="margin-top: 80rpx;" v-if="chargeOrderList.length==0">
				<uv-empty mode="list" icon="/static/images/noData.png" text="暂无服务订单"
					style="height: 420rpx;width: 420rpx;"></uv-empty>
			</view>
		</view>
		<view class="" v-else>
			<view class="" v-for="item in washOrderList" v-if="washOrderList.length>0" @click="toWashDetail(item)"
				style="margin: 20rpx auto; min-height: 450rpx;width: 702rpx;background-color: #FFFFFF;position: relative;">
				<view class="" style="height: 72rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;
				padding: 0 30rpx;background-color: #FFF6ED;font-size: 24rpx;color: #FF7E32;">
					<view class="">
						<text>洗车订单</text>
						<text style="margin-left: 8rpx;">{{item.orderCode}}</text>
					</view>
					<text>{{filterOrder(item.status)}}</text>
				</view>
				<view class="" style="min-height: 378rpx;width: 100%;padding: 30rpx;">
					<view class="" style="font-size: 32rpx;font-weight: 600;color: #222222;">
						{{item.shopName}}
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">支付时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.createTime}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">支付类型:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{filterPay(item.payType)}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">洗车模式:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.washModeName}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">车牌号码:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.carNumber}}</text>
					</view>
					<view class="" style="width: 702rpx;height: 1px;background-color: #EFEFEF;margin: 10rpx auto;">

					</view>
					<view class="" style="display: flex;align-items: center;margin-top: 14rpx;justify-content: space-between;">
						<view class="" style="display: flex;align-items: center;">
							<view class="" style="display: flex;align-items: center;">
								<text style="font-size: 20rpx;color: #222222;">洗车费用</text>
								<text style="font-size: 26rpx;color: #222222;">￥</text>
								<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.price}}</text>
							</view>
							<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
								<text style="font-size: 20rpx;color: #222222;">卡券减免</text>
								<text style="font-size: 26rpx;color: #222222;">￥</text>
								<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.discounts}}</text>
							</view>
						</view>
						
						<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
							<text style="font-size: 20rpx;color: #222222;">实付</text>
							<text style="font-size: 26rpx;color: #222222;">￥</text>
							<text style="font-size: 40rpx;color: #FF7E32;font-weight: 600;">{{item.amount}}</text>
						</view>
					</view>
					<view class="" style="font-size: 22rpx;color: #999999;height: 50rpx;display: flex;align-items: flex-end;" @click.stop="toWashQuestion(item.id)">
						<text style="text-align: center;">订单反馈</text>
						<uv-icon name="arrow-right" size="10" color="#999999"></uv-icon>
					</view>
					<view class="" v-if="(item.status == 2)&&!item.userInvoiceApply.id" @click.stop="toInvoice(item)"
						style="position: absolute;right: 20rpx;bottom: 10rpx;width: 144rpx;height: 61rpx;background-color: #FFF6ED;display: flex;align-items: center;justify-content: center;border-radius: 60rpx;">
						<text style="color: #FF7E32;font-size: 28rpx;" >开发票</text>
					</view>
					<view class="" v-if="item.userInvoiceApply.id" @click.stop="lookInvice1(item)"
						style="position: absolute;right: 20rpx;bottom: 10rpx;width: 144rpx;height: 61rpx;background-color: #FFF6ED;display: flex;align-items: center;justify-content: center;border-radius: 60rpx;">
						<text style="color: #FF7E32;font-size: 28rpx;" >查看发票</text>
					</view>
				</view>
			</view>
			<view class="" style="margin-top: 80rpx;" v-if="washOrderList.length==0">
				<uv-empty mode="list" icon="/static/images/noData.png" text="暂无服务订单"
					style="height: 420rpx;width: 420rpx;"></uv-empty>
			</view>
		</view>
		<view class="" style="height: 180rpx;">
			
		</view>
		<m-tabbar></m-tabbar>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list: ['充电订单', '洗车订单'],
				current: 0,
				listTab: [{
					name: '全部'
				}, {
					name: '进行中',
				}, {
					name: '已完成',
				}],
				params: {
					pageNumber: 1,
					pageSize: 10,
					chargeStatusList: []
				},
				chargeOrderList: [],
				washOrderList: [],
				bool: true,
				params1: {
					pageNumber: 1,
					pageSize: 10,
					statusList: [1,2]
				}
			}
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options);
			if(options){
				if(options.type==2){
					this.chooseType(1)
				}else{
					this.chooseType(0)
				}
			}else {
				uni.showLoading({
					title:'加载中...'
				})
				this.params.pageNumber = 1
				this.bool = true
				this.$iBox.http('getChargingPileOrderList', this.params)({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.chargeOrderList = res.data.list
				})
			}
			
		},
		methods: {
			...mapActions('login',['pushOrderDetail','pushPNumer','pushIds']),
			filterTime(e){
				
				let s = this.$moment(e.startTime)
				let b = this.$moment(e.endTime)
				let c = s.diff(b,'hours')
			},
			lookInvice(e){
				let a = {
					type:this.current,
					status:0
				}
				this.pushIds(a)
				uni.navigateTo({
					url:'/pages/fapiao/fapiao'
				})
			},
			lookInvice1(e){
				let a = {
					type:this.current,
					status:0
				}
				this.pushIds(a)
				uni.navigateTo({
					url:'/pages/fapiao/fapiao'
				})
			},
			filterPay(e) {
				let list = [{
					id: 1,
					name:'微信支付'
				}, {
					id: 2,
					name:'会员支付'
				}, {
					id: 3,
					name:'支付宝支付'
				}, {
					id: 4,
					name:'加盟店用户'
				}, {
					id: 5,
					name:'K码'
				}, {
					id: 6,
					name:'易捷支付'
				}, {
					id: 7,
					name:'系统添加'
				}]
				let type = list.filter(item => {
					return item.id == e
				})[0].name
				
				return type
			},
			filterOrder(e) {
				let list = [{
					id: 0,
					name:'未支付'
				}, {
					id: 1,
					name:'已支付'
				}, {
					id: 2,
					name:'已完成'
				}, {
					id: 3,
					name:'已过期'
				}, {
					id: 4,
					name:'正在申请'
				}, {
					id: 5,
					name:'已退款'
				}, {
					id: 6,
					name:'申请拒绝'
				}]
				let type = list.filter(item => {
					return item.id == e
				})[0].name
				
				return type
			},
			toWashQuestion(e){
				uni.navigateTo({
					url:'/pages/order/washQuestion?id='+e
				})
			},
			toPowerQuestion(e){
				uni.navigateTo({
					url:'/pages/order/powerQuestion?id='+e
				})
			},
			toPowerDetail(e){
				console.log('100');
				if(e.chargeStatus==4){
					this.pushOrderDetail(e)
					uni.navigateTo({
						url:'/pages/order/powerOrderDetail'
					})
				}else{
					this.pushPNumer(e.deviceId)
					uni.navigateTo({
						url:'/pages/resultPage/resultPage'
					})
				}
				
			},
			toInvoice(e){
				console.log('e');
				// let type = this.current==0?3:1
				// uni.navigateTo({
				// 	url:'/pages/fapiao/invoice?id='+e.id+'&type='+ type
				// })
				uni.navigateTo({
					url:'/pages/fapiao/fapiao'
				})
			},
			toWashDetail(e){
				console.log('1001');
				this.pushOrderDetail(e)
				uni.navigateTo({
					url:'/pages/order/washOrderDetail'
				})
				
			},
			chooseType(index) {
				this.current = index;
				if (index == 0) {
					this.params.pageNumber = 1
					this.bool = true
					this.getRechargeOrder()
				} else {
					this.params1.pageNumber = 1
					this.bool = true
					this.getWahOrderList()
				}
			},
			getRechargeOrder() {
				uni.showLoading({
					title:'加载中...'
				})
				this.$iBox.http('getChargingPileOrderList', this.params)({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.chargeOrderList = res.data.list
				})
			},
			getWahOrderList() {
				uni.showLoading({
					title:'加载中...'
				})
				this.$iBox.http('getCarWashCarOrder', this.params1)({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.washOrderList = res.data.list
				})
			},
			chooseTab(e) {
				console.log(e);
				if (this.current == 0) {
					if (e.index == 0) {
						this.params.chargeStatusList = []
					} else if (e.index == 1) {
						this.params.chargeStatusList = [2, 3]
					} else {
						this.params.chargeStatusList = [4]
					}
					this.params.pageNumber = 1
					this.bool = true
					this.getRechargeOrder()
				} else if (this.current == 1) {
					if (e.index == 0) {
						this.params1.statusList = [1,2]
					} else if (e.index == 1) {
						this.params1.statusList = [1]
					} else {
						this.params1.statusList = [2]
					}
					this.params1.pageNumber = 1
					this.bool = true
					this.getWahOrderList()
				}

			}
		},
		// // 上拉加载
		onReachBottom() {
		
			if (this.bool) {
				console.log(this.changeBtnId,'this.changeBtnId');
				if(this.current==0){
					++this.params.pageNumber
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getChargingPileOrderList', this.params)({
						method: 'post'
					}).then(res => {
							
						let new_list = this.chargeOrderList.concat(res.data.list)
						this.chargeOrderList = new_list
						if (this.chargeOrderList.length == res.data.count) {
							this.bool = false
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}else if(this.current==1){
					++this.params1.pageNumber
							
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getCarWashCarOrder', this.params1)({
						method: 'post'
					}).then(res => {
							
						let new_list = this.washOrderList.concat(res.data.list)
						this.washOrderList = new_list
						if (this.washOrderList.length == res.data.count) {
							this.bool = false
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			}
		
		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;

	}

	view {
		box-sizing: border-box;
	}
</style>