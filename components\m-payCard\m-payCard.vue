<template>
	<view class="payBox">
		<view class="" style="display: flex;align-items: center;justify-content: space-between;">
			<p style="margin: 0rpx auto;padding: 30rpx;font-size: 26rpx;width: fit-content;">
				选择一种支付方式</p>
			
		</view>
		
		<radio-group @change="radioChange">
			<label class="wx" v-for="(item, index) in items" :key="item.value">
				<view class="" style="display: flex;align-items: center;">
					<image src="../../static/images/weixin.png" v-if="item.value=='weixin'" style="width: 50rpx;height: 50rpx;" mode=""></image>
					<image src="../../static/images/huiy.png" v-else style="width: 50rpx;height: 50rpx;" mode=""></image>
					<view style="margin-left: 20rpx;">{{item.name}}
					<text v-if="item.value=='huiyuanka'">(余额：{{centerUser.userBalance.balance}}元)</text></view>
				</view>

				<view>
					<radio :value="item.value" :checked="index === current" />
				</view>
			</label>
		</radio-group>

		<view class="btn_pay" @click="toPay">
			<view class="btn_style" :style="{background:'#ffb800',color:'#FFFFFF'}">
				<text>确认支付</text>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				items: [{
					name: '微信支付',
					value: 'weixin'
				}, {
					name: '会员支付',
					value: 'huiyuanka'
				}],
				current: 0,
				centerUser:null
			};
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		async mounted() {
			await this.$onLaunched;
			// 每次更新重新获取个人信息并保存
			this.$iBox.http('getUserInfo', {
			})({
				method: 'get'
			}).then(resUser => {
				let userInfo = resUser.data
				userInfo.session_key = this.userInfo.session_key
				userInfo.token = this.userInfo.token
				this.centerUser = userInfo
				this.updateUserInfo(userInfo)
				
			})

			
		},

		methods: {
			...mapActions('login', ['updateUserInfo']),
			radioChange: function(evt) {
				console.log(evt);
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			toPay() {
				this.$emit('toPay',this.items[this.current].value )
			}
		}

	}
</script>
<style>
	view {
		box-sizing:border-box;
	}
</style>
<style lang="scss" scoped>
	.payBox {
		min-height: 800rpx;
		position: relative;
		width: 100%;
		margin-top: 60rpx;
		z-index: 9999999999;

		.wx {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
		}

		.btn_pay {
			position: absolute;
			bottom: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100vw;
			.btn_style {
				border-radius: 50rpx;
				width: 80%;
				margin: 30rpx auto;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 20rpx;
				height: 80rpx;
			}
		}
	}
</style>