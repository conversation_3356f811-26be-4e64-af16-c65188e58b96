<template>
	<view>
		<view class="" style="padding: 0 24rpx;font-size: 34rpx;">
			<text style="font-size: 40rpx;font-weight: 600;color: #222222;">{{pNumber?pNumber:''}}号枪</text>
		</view>
		<view class="" style="display: flex;align-items: center;padding: 0 24rpx;margin-top: 10rpx;">
			<image src="/static/images/rgicon.png" style="width: 48rpx;height: 48rpx;" mode=""></image>
			<text style="color: #222222;font-size: 36rpx;margin-left: 8rpx;">{{detail.parkName}}</text>
		</view>
		<view class="charts-box" style="position:relative;" v-if="current==1">
			<qiun-data-charts type="gauge" :opts="opts" :chartData="chartData" />

		</view>
		<view class=""
			style="height: 300rpx;display: flex;align-items: center;justify-content: center;flex-direction: column;"
			v-if="current==0">
			<uv-loading-icon mode="spinner" size="40" inactive-color="#00C7A3"></uv-loading-icon>
			<view class=""
				style="margin:0 auto;font-size: 68rpx;color: #00C7A3;height: 200rpx;width: 200rpx;display: flex;align-items: center;justify-content: center;"
				v-if="current==0">
				{{timek}}秒
			</view>
		</view>
		<view class=""
			style="width: 100%;display: flex;align-items: center;justify-content: center;color: #00C7A3;margin-top: -60rpx;font-size: 44rpx;">
			<!-- 	<text
				v-if="!detail.chargingPileOrder||detail.chargingPileOrder.chargeStatus==1||(detail.chargingPileOrder.equipChargeStatus.currentA==0)">启动中</text>
			<text
				v-if="detail.chargingPileOrder.chargeStatus==2&&detail.chargingPileOrder.equipChargeStatus.voltageA>0&&detail.chargingPileOrder.equipChargeStatus.currentA>0">正在充电</text>
			 -->
			<text v-if="detail.chargingPileOrder.chargeStatus==5">故障</text>

			<view class=""
				style="width: 702rpx;margin: 0rpx auto;padding: 30rpx;display: flex;align-items: center;justify-content: center;"
				v-if="current==0">
				<!-- <uv-steps :current="current" activeColor="#00C7A3">
					<uv-steps-item title="握手通讯"></uv-steps-item>
					<uv-steps-item title="参数协商"></uv-steps-item>
				</uv-steps> -->
				<text style="font-size: 48rpx;">启动充电</text>
			</view>
			<view class="" v-if="detail.chargingPileOrder.chargeStatus==2&&current==1">
				<text v-if="detail.chargingPileOrder.equipChargeStatus.leftTime"
					style="font-size: 26rpx;">剩余充电时间预估{{detail.chargingPileOrder.equipChargeStatus.leftTime}}分钟</text>
				<text v-else>正在充电</text>

			</view>

		</view>
		<view class=""
			style="width: 702rpx;height: 170rpx;border-radius: 16rpx;margin: 30rpx auto;padding: 30rpx;background-color: #FFFFFF;">
			<view class="">
				<text style="font-size: 28rpx;color: #999999;">车辆信息</text>
				<text
					style="font-size: 28rpx;color: #222222;margin-left: 40rpx;">{{detail.chargingPileOrder.plateNo}}</text>
			</view>
			<view class="">
				<text style="font-size: 28rpx;color: #999999;">订单编号</text>
				<text
					style="font-size: 28rpx;color: #222222;margin-left: 40rpx;">{{detail.chargingPileOrder.orderNo}}</text>
			</view>
		</view>
		<view class=""
			style="height: 380rpx;width: 702rpx;background-color: #FFFFFF;border-radius: 16rpx;margin: 30rpx auto;padding: 0 30rpx;color: #222222;">
			<view class="" style="height: 190rpx;width: 100%;border-bottom: #EFEFEF;display: flex;">
				<view class=""
					style="width: 33%;height: 100%;display: flex;flex-direction: column;align-items: center;">
					<p><text
							style="font-size: 36rpx;">{{detail.chargingPileOrder&&detail.chargingPileOrder.equipChargeStatus?((detail.chargingPileOrder.equipChargeStatus.currentA*detail.chargingPileOrder.equipChargeStatus.voltageA)/1000).toFixed(0):''}}</text><text
							style="font-size: 24rpx;">KW</text></p>
					<text style="color: #999999;font-size: 28rpx;margin-top: 20rpx;">充电功率</text>
				</view>
				<view class=""
					style="width: 33%;height: 100%;display: flex;flex-direction: column;align-items: center;">
					<p><text
							style="font-size: 36rpx;">{{detail.chargingPileOrder.equipChargeStatus?detail.chargingPileOrder.equipChargeStatus.currentA:''}}</text><text
							style="font-size: 24rpx;">A</text></p>
					<text style="color: #999999;font-size: 28rpx;margin-top: 20rpx;">充电电流</text>
				</view>
				<view class=""
					style="width: 33%;height: 100%;display: flex;flex-direction: column;align-items: center;">
					<p><text
							style="font-size: 36rpx;">{{detail.chargingPileOrder.equipChargeStatus?detail.chargingPileOrder.equipChargeStatus.voltageA:''}}</text>v
					</p>
					<text style="color: #999999;font-size: 28rpx;margin-top: 20rpx;">充电电压</text>
				</view>
			</view>
			<view class="" style="height: 190rpx;width: 100%;border-bottom: #EFEFEF;display: flex;">
				<view class=""
					style="width: 33%;height: 100%;display: flex;flex-direction: column;align-items: center;">
					<p><text
							style="font-size: 36rpx;">{{detail.chargingPileOrder.equipChargeStatus?detail.chargingPileOrder.equipChargeStatus.totalPower:''}}</text><text
							style="font-size: 24rpx;">度</text></p>
					<text style="color: #999999;font-size: 28rpx;margin-top: 20rpx;">充电量</text>
				</view>
				<view class=""
					style="width: 33%;height: 100%;display: flex;flex-direction: column;align-items: flex-start;margin-left: 60rpx;">
					<view style="display: flex;align-items: center;">
						<view style="color: #999999;font-size:24rpx;width: 80rpx;">总费用:</view><text
							style="font-size: 24rpx;">{{detail.chargingPileOrder.equipChargeStatus?detail.chargingPileOrder.equipChargeStatus.totalMoney:''}}</text><text
							style="font-size: 24rpx;">元</text>
					</view>
					<view style="font-size: 24rpx;display: flex;align-items: center;">
						<view style="color: #999999;width: 80rpx;">电费: </view>
						{{detail.chargingPileOrder.equipChargeStatus?detail.chargingPileOrder.equipChargeStatus.elecMoney:''}}元
					</view>
					<view style="font-size: 24rpx;display: flex;align-items: center;">
						<view style="color: #999999;width: 80rpx;">服务费:</view>
						{{detail.chargingPileOrder.equipChargeStatus?detail.chargingPileOrder.equipChargeStatus.serviceMoney:''}}元
					</view>


				</view>
				<view class="" @click="chooseValue"
					style="width: 33%;height: 100%;display: flex;flex-direction: column;align-items: center;">
					<view style="display: flex;"> <text style="color:#00C7A3;">{{powerValue.value}}%</text>
						<uv-icon name="arrow-down" color="#333333" size="12"></uv-icon>
					</view>
					<text style="color: #999999;font-size: 28rpx;margin-top: 20rpx;">充电限值</text>
				</view>
			</view>
			<view class="" @click="stopPower"
				v-if="detail.chargingPileOrder&&detail.chargingPileOrder.chargeStatus==2&&detail.chargingPileOrder.equipChargeStatus.currentA>0"
				style="height: 88rpx;width: 580rpx;display: flex;align-items: center;justify-content: center;background-color: #00C7A3;color: #FFFFFF;border-radius: 30rpx;margin: 20rpx auto;">
				结束充电
			</view>
			<!-- 	<view class=""
				v-if="detail.chargingPileOrder.chargeStatus==1||detail.chargingPileOrder.equipChargeStatus.currentA==0||!detail.chargingPileOrder"
				style="height: 88rpx;width: 580rpx;display: flex;align-items: center;justify-content: center;background-color: #00C7A3;color: #FFFFFF;border-radius: 30rpx;margin: 20rpx auto;">
				启动中
			</view> -->
		</view>

		<uv-popup ref="popup" mode="bottom" round="16">
			<view style="height: 500rpx;width: 100%;padding: 30rpx 24rpx;position: relative;">
				<view class="" style="display: flex;flex-wrap: wrap;justify-content: center">
					<view class="" v-for="item in powerValues"
						style="height: 70rpx;width: 50%;margin-top: 20rpx;display: flex;justify-content: center;">
						<view class="" @click="choosePower(item)"
							:style="powerValue.id==item.id?'background:#00C7A3;':'background:#EFEFEF;'"
							style="height: 70rpx;width: 280rpx;border-radius: 12rpx;background-color: #00C7A3;display: flex;align-items: center;padding: 0 20rpx;">
							<view class="" v-if="powerValue.id!=item.id"
								style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

								<uv-icon name="checkmark" color="#999999" size="16"></uv-icon>
							</view>
							<view class="" v-if="powerValue.id==item.id"
								:style="powerValue.id==item.id?'background:#00C7A3;':''"
								style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

								<uv-icon name="checkmark" color="#FFFFFF" size="16"></uv-icon>
							</view>
							<text>{{item.value}}</text>
						</view>
					</view>
				</view>
				<view class=""
					style="width: 100%;position: absolute;bottom: 30rpx;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="sureChoose"
						style="display: flex;align-items: center;justify-content: center;width: 560rpx;height: 88rpx;background-color: #00C7A3;border-radius: 16rpx;">
						<text style="color: #FFFFFF;">确定</text>
					</view>
				</view>
			</view>
		</uv-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				pNumber: '',
				chartData: {},
				timeData: {},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['gauge'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#00C7A3", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: undefined,
					title: {
						name: "",
						fontSize: 45,
						color: "#222222",
						offsetY: 0
					},
					subtitle: {
						name: "",
						fontSize: 15,
						color: "#00C7A3",
						offsetY: 0
					},
					extra: {
						gauge: {
							type: "progress",
							width: 20,
							labelColor: "#666666",
							startAngle: 0.75,
							endAngle: 0.25,
							startNumber: 0,
							endNumber: 100,
							labelFormat: "",
							splitLine: {
								fixRadius: -10,
								splitNumber: 10,
								width: 15,
								color: "#FFFFFF",
								childNumber: 5,
								childWidth: 12
							},
							pointer: {
								width: 24,
								color: "auto"
							}
						}
					}
				},
				detail: '',
				timeOut: 1,
				powerValues: [{
					id: 1,
					value: 80
				}, {
					id: 2,
					value: 90
				}, {
					id: 3,
					value: 95
				}, {
					id: 4,
					value: 100
				}],
				powerValue: null,
				current: 0,
				time1: null,
				time2: null,
				order: false,
				time3: null,
				timek: 90,
				orderStatus:1,
				currentA:0
				//chargeStatus： 1启动中，2充电中，3停止中，4已结束，5未知
				//connectorType ：0.离网，1.空闲，2.占用（未充电），3.占用（充电中），4.占用（预约锁定），255.未知
			};
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop', 'pNum', 'car']),
		},
		watch: {
			timek:{
				handler(newVal, oldVal){
					if(newVal<1){
						this.current = 1
					}
				},
				immediate:true
			}
		},
		async onLoad() {
			await this.$onLaunched;
			let self = this
			this.pNumber = this.pNum
			this.powerValue = this.powerValues[3]
			this.time3 = setInterval(function() {
				self.timek--
			}, 1000)
			// 先查一遍若是充电中直接展示
			this.timeInfo = setInterval((function target() {
				self.getDetail()
			}), 5000)
			self.$iBox.http('getConnectorInfo', {
				connectorId: self.pNum
			})({
				method: 'get'
			}).then(res => {
				if (res.data.chargingPileOrder) {
					this.order = res.data.chargingPileOrder
					this.orderStatus = 2
				}
				if (res.data.chargingPileOrder && res.data.chargingPileOrder.equipChargeStatus &&
					res.data.chargingPileOrder.equipChargeStatus.currentA > 0) {
					self.time3 && clearTimeout(self.time3)
					this.detail = res.data
					let res1 = {
						categories: [],
						series: [{
							name: "完成率",
							data: this.detail.chargingPileOrder.equipChargeStatus &&
								this.detail
								.chargingPileOrder.equipChargeStatus.soc ? this.detail
								.chargingPileOrder.equipChargeStatus.soc / 100 : 0
						}]
					};
					this.chartData = JSON.parse(JSON.stringify(res1));
					if (this.detail.chargingPileOrder && this.detail.chargingPileOrder
						.equipChargeStatus && this
						.detail.chargingPileOrder.equipChargeStatus.soc) {
						this.opts.title.name = this.detail.chargingPileOrder.equipChargeStatus
							.soc + "%"
					}
					self.current = 1
				}
			})
			
			this.time1 = setTimeout(item => {
				let res1 = {
					categories: [],
					series: [{
						name: "完成率",
						data: 0
					}]
				};
				this.chartData = JSON.parse(JSON.stringify(res1));
				this.opts.title.name = ''
			}, 50)

		},

		onReady() {

		},
		methods: {
			...mapActions('login', ['pushCar', 'updateUserInfo', 'pushPNumer']),
			chooseValue() {
				this.$refs.popup.open()
			},
			choosePower(e) {
				this.powerValue = e
			},
			sureChoose() {

				this.$iBox.http('updateSetPower', {
					id: this.detail.chargingPileOrder.id,
					setPower: this.powerValue.value
				})({
					method: 'post'
				}).then(res => {
					this.$refs.popup.close()
				})
			},
			getDetail() {
				this.$iBox.http('getConnectorInfo', {
					connectorId: this.pNumber
				})({
					method: 'get'
				}).then(res => {
					if (res.data.chargingPileOrder) {
						this.order = res.data.chargingPileOrder
						this.orderStatus = 2
					}else{
						if(this.orderStatus==2){
							this.orderStatus = 3
						}
						
					}
					
					if (res.data.chargingPileOrder && res.data.chargingPileOrder.equipChargeStatus && res.data.chargingPileOrder.equipChargeStatus.currentA > 0) {
						this.time3 && clearInterval(this.time3)
						this.current = 1
						console.log(this.current);
					}

					if ((res.data.chargingPileOrder && res.data.chargingPileOrder.chargeStatus == 5) || res.data
						.status == 255) {
						uni.showModal({
							title: '提示',
							content: '检查车辆是否异常、重新插枪再次启动',
							showCancel: false,
							success: res => {
								uni.navigateBack()
							}
						})
						return
					}
					console.log(this.orderStatus,'this.orderStatus');
					if (this.orderStatus==3) {
						this.timeInfo && clearInterval(this.timeInfo)
						uni.navigateTo({
							url: './lastPage?id=' + this.order.id
						})
						return
					}

					clearTimeout(this.time1)
					this.detail = res.data
					let res1 = {
						categories: [],
						series: [{
							name: "完成率",
							data: this.detail.chargingPileOrder.equipChargeStatus && this.detail
								.chargingPileOrder.equipChargeStatus.soc ? this.detail
								.chargingPileOrder.equipChargeStatus.soc / 100 : 0
						}]
					};
					this.chartData = JSON.parse(JSON.stringify(res1));
					if (this.detail.chargingPileOrder && this.detail.chargingPileOrder.equipChargeStatus && this
						.detail.chargingPileOrder.equipChargeStatus.soc) {
						this.opts.title.name = this.detail.chargingPileOrder.equipChargeStatus.soc + "%"
					}
				})

			},
			stopPower() {
				this.$iBox.http('stopCharge', {
					id: this.detail.chargingPileOrder.id
				})({
					method: 'post'
				}).then(res => {
					this.timeInfo && clearInterval(this.timeInfo)
					uni.navigateTo({
						url: './lastPage?id=' + this.detail.chargingPileOrder.id
					})
				})
			}
		},
		onUnload() {
			this.timeInfo && clearInterval(this.timeInfo)
			// this.pushPNumer('')
		}
	}
</script>

<style>
	page {
		background: linear-gradient(180deg, #EAFFF9 0%, #FFFFFF 100%);

	}

	view {
		box-sizing: border-box;
	}

	.charts-box {
		width: 100%;
		height: 300px;
	}
</style>