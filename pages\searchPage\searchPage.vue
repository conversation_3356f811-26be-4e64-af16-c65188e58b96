<template>
	<view>
		<view class="" style="padding: 30rpx;">
			<uv-search placeholder="请搜索服务站点" @search="search" v-model="keyName"></uv-search>
		</view>
		<view class="" style="display: flex;align-items: center;padding:10rpx 30rpx;">
			<view class="btnT" v-for="item in changeBtnList" :key="item.id" @click="changeBtn(item)"
				:style="item.id == changeBtnId?'background: #C8FFEF;':'background: #e6e6e6;color:#999999;'" style="">
				{{item.name}}
			</view>
		</view>
		<view class="shopList">
			<m-powerList :list="shopListArr" :source="'search'" v-if="hackReset&&changeBtnId==1"></m-powerList>
			<m-washCarList :list="shopListArr" v-if="hackReset&&changeBtnId==2"></m-washCarList>
		</view>
		<view class="" style="height: 160rpx;">
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				changeBtnList: [{
						id: 1,
						name: '充电服务'
					},
					{
						id: 2,
						name: '洗车服务'
					}
				],
				bool: true,
				shopListArr: [],
				changeBtnId: 1,
				hackReset: false,
				if_login: false,
				params: {
					pageNumber: 1,
					pageSize: 10,
					longitude: '',
					latitude: '',
					sortBy: 'distance'
				},
				params1: {
					pageNumber: 1,
					pageSize: 10,
					longitude: '',
					latitude: '',
					shopName: null,
					telephone: null,
					address: null,
					remark: null
				},
				keyName: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop']),
		},
		onLoad(options) {
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			if(options.type=='charge'){
				this.getChargeShopList()
				this.changeBtnId = 1
			}else {
				this.changeBtnId = 2
				this.getWashShopList()
			}
			
		},
		methods: {
			...mapActions('login', ['toLogin', 'pushShopList', 'pushShop']),
			changeBtn(e) {
				this.changeBtnId = e.id

				console.log(e);
				if (e.id == 1) {
					this.getChargeShopList()
				} else if (e.id == 2) {
					this.getWashShopList()
				}
			},
			search() {
				if (this.changeBtnId == 1) {
					this.params.shortName = this.keyName
					this.getChargeShopList()
				} else {
					this.params1.shopName = this.keyName
					this.getWashShopList()
				}

			},
			getWashShopList() {
				// 第一次调起授权， 成功后拉起微信收货地址页面
				uni.showLoading({
					title: '加载中...'
				})

				wx.getLocation({
					success: res => {
						this.params1.latitude = res.latitude
						this.params1.longitude = res.longitude
						this.params1.pageNumber = 1
						this.$iBox
							.http('getCarWashShopList', this.params1)({
								method: 'post'
							}).then(res => {
								uni.hideLoading()
								this.shopListArr = res.data.list
								this.pushShopList(res.data.list);

							});

					},
					fail: err => {
						wx.getSetting({
							success: res2 => {
								if (!res2.authSetting['scope.userLocation']) {
									uni.hideLoading()
									wx.authorize({
										scope: 'scope.userLocation',
										success: () => {
											// 第一次调起授权， 成功后拉起微信收货地址页面
											wx.getLocation({
												success: res => {
													this.params1.latitude =res.latitude
													this.params1longitude = res.longitude
													this.params1.pageNumber = 1
													this.$iBox.http(
														'getCarWashShopList',this.params1)({
														method: 'post'
													}).then(
														res => {
															this.shopListArr =res.data.list
															this.pushShopList(res.data.list);
														});

												}
											});
										},
										fail: () => {
											uni.hideLoading()
											wx.showModal({
												title: '提示',
												content: '若点击不授权，将无法正常定位门店使用本软件',
												cancelText: '不授权',
												cancelColor: '#999',
												confirmText: '授权',
												confirmColor: '#f94218',
												success(res) {

													if (res.confirm) {
														console.log('lalala',
															res)
														uni.openSetting({
															success(
																res) {}
														});
													} else
													if (res.cancel) {
														console.log('用户点击取消');
														uni.showModal({
															title: '提示',
															content: '未授权定位，请重新扫码进入！',
															success(
																res) {

															}
														})
													}
												}
											});
										}
									});
								} else {
									//第一次调起授权， 成功后拉起微信收货地址页面
									uni.hideLoading()
									wx.getLocation({
										success: res => {
											this.params1.latitude = res.latitude
											this.params1.longitude = res.longitude
											this.params1.pageNumber = 1
											this.$iBox
												.http('getCarWashShopList', this
													.params1)({
													method: 'post'
												}).then(res => {
													this.shopListArr = res.data
														.list
													this.pushShopList(res.data
														.list);
												});
										}
									});
								}
							}
						});
					}
				});
			},
			getChargeShopList() {
				// 第一次调起授权， 成功后拉起微信收货地址页面
				uni.showLoading({
					title: '加载中...'
				})

				wx.getLocation({
					success: res => {
						this.params.latitude = res.latitude
						this.params.longitude = res.longitude
						this.params.sortBy = 'distance'
						this.$iBox
							.http('getChargingPileParkList', this.params)({
								method: 'post'
							}).then(res => {
								uni.hideLoading()
								this.shopListArr = res.data.list
								this.pushShopList(res.data.list);
							});

					},
					fail: err => {
						wx.getSetting({
							success: res2 => {
								if (!res2.authSetting['scope.userLocation']) {
									wx.authorize({
										scope: 'scope.userLocation',
										success: () => {
											uni.hideLoading()
											// 第一次调起授权， 成功后拉起微信收货地址页面
											wx.getLocation({
												success: res => {
													this.params.latitude =
														res.latitude
													this.params.longitude =
														res.longitude

													this.params
														.pageNumber = 1
													this.params
														.sortBy =
														'distance'
													this.$iBox.http(
														'getChargingPileParkList',
														this.params
													)({
														method: 'post'
													}).then(
														res => {
															this.shopListArr =
																res
																.data
																.list
															this.pushShopList(
																res
																.data
																.list
															);
														});

												}
											});
										},
										fail: () => {
											uni.hideLoading()
											wx.showModal({
												title: '提示',
												content: '若点击不授权，将无法正常定位门店使用本软件',
												cancelText: '不授权',
												cancelColor: '#999',
												confirmText: '授权',
												confirmColor: '#f94218',
												success(res) {

													if (res.confirm) {
														console.log('lalala',
															res)
														uni.openSetting({
															success(
																res) {

															}
														});
													} else
													if (res.cancel) {
														console.log('用户点击取消');
														uni.showModal({
															title: '提示',
															content: '未授权定位，请重新扫码进入！',
															success(
																res) {

															}
														})
													}
												}
											});
										}
									});
								} else {
									uni.hideLoading()
									//第一次调起授权， 成功后拉起微信收货地址页面
									wx.getLocation({
										success: res => {
											this.params.latitude = res.latitude
											this.params.longitude = res.longitude
											this.params.pageNumber = 1
											this.params.sortBy = 'distance'
											this.$iBox
												.http('getChargingPileParkList',
													this
													.params)({
													method: 'post'
												}).then(res => {
													this.shopListArr = res.data
														.list
													this.pushShopList(res.data
														.list);
												});

										}
									});
								}
							}
						});
					}
				});
			},
		},
		// // 上拉加载
		onReachBottom() {
			if (this.bool) {
				console.log(this.changeBtnId,'this.changeBtnId');
				if(this.changeBtnId==1){
					++this.params.pageNumber
					this.status = 'loadmore'
							
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getParkingLotList', this.params)({
						method: 'post'
					}).then(res => {
							
						let new_list = this.shopListArr.concat(res.data.list)
						this.shopListArr = new_list
						if (this.shopListArr.length == res.data.count) {
							this.bool = false
							this.status = 'nomore'
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}else if(this.changeBtnId==2){
					++this.params1.pageNumber
					this.status = 'loadmore'
							
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getCarWashShopList', this.params1)({
						method: 'post'
					}).then(res => {
							
						let new_list = this.shopListArr.concat(res.data.list)
						this.shopListArr = new_list
						if (this.shopListArr.length == res.data.count) {
							this.bool = false
							this.status = 'nomore'
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			}

		}
	}
</script>
<style>
	page {
		background-color: #F4F6F8;
	}
	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">
	.waterfall {
		.itemBox {
			width: 100%;
			margin-top: -30rpx;
			border-top-right-radius: 32rpx;
			border-top-left-radius: 32rpx;
			padding: 24rpx 24rpx;

			.itemBoxInner {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 70rpx;
				position: relative;

				.itemBoxInner_box {

					height: 200rpx;
					width: 340rpx;
					background-color: #e6edf6;
					border-radius: 15rpx;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: space-around;

					.boxStyle {
						height: 200rpx;
						width: 100%;
						padding-left: 20rpx;
						padding-top: 10rpx;
						z-index: 9;
						color: #131313;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;

					}



					.boxStyle_around {
						width: 140rpx;
						height: 140rpx;
						border-radius: 50%;
						background-color: #e6edf6;
						position: absolute;
						margin: 0 auto;
						left: 0;
						right: 0;
						top: -56rpx;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

			}

			.itemBoxInner1 {
				display: flex;
				align-items: center;
				justify-content: center;

				.itemBoxInner1_box {
					height: 200rpx;
					width: 710rpx;
					background-color: #ffb800;
					border-radius: 15rpx;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #2A2B30;

					.boxStyle {
						height: 100%;
						width: 100%;
						padding-left: 20rpx;
						// padding-top: 40rpx;
						z-index: 9;
						//
						color: #FFFFFF;
						display: flex;
						flex-direction: column;
						//
						justify-content: space-around;
						align-items: flex-start;
					}
				}

			}
		}
	}

	.waterfall-item {
		overflow: hidden;
		margin-top: 10px;
		border-radius: 6px;
	}




	.titleBox {
		display: flex;
		height: 100rpx;
		align-items: center;
		padding: 0 10rpx;
		justify-content: space-between;

		.btnT {
			width: 112rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8rpx;
			color: #077E6C;
			background-color: #C8FFEF;
			font-size: 22rpx;
			margin-right: 20rpx;

		}

	}

	.btnT {
		width: 112rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
		color: #077E6C;
		background-color: #C8FFEF;
		font-size: 22rpx;
		margin-right: 20rpx;

	}
</style>