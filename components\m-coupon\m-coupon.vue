<template>
	<view style="">
		<view class="" style="height: 220rpx;width: 702rpx;margin: 20rpx auto;display: flex;adding: 10rpx;background-color: #FFFFFF;border-radius: 26rpx;align-items: center;justify-content: center;position: relative;" v-if="item.status==1" v-for="item in couponList">
			<view class="" style="height: 200rpx;width: 230rpx;background-color: #FFF2F2;border-radius: 26rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;">
				<view class="" style="display: flex;align-items: center;">
					<text style="color: #FF7E32;font-size: 38rpx;font-weight: 600;">￥</text>
					<text style="color: #FF7E32;font-size: 100rpx;font-weight: 600;">{{item.amount}}</text>
				</view>
				<view class="">
					<text style="color: #FF7E32;font-size: 22rpx;font-weight: 600;">{{item.description}}</text>
				</view>
			</view>
			<view class="" style="height: 120rpx;width: 430rpx;display: flex;flex-direction: column;justify-content: space-between;margin-left: 40rpx;">
				<text style="font-size: 36rpx;color: #222222;">{{item.couponName}}</text>
				<text style="font-size: 22rpx;color: #999999;">截止:{{item.couponUseEndTime}}</text>
				<view class="" style="display: flex;align-items: center;font-size: 22rpx;color: #999999;flex-wrap: wrap;">
					适用店铺:<text v-for="item1 in item.shopList">{{item1.shopName}}/</text>
				</view>
			</view>
			
			<view class="" @click="choose(item)" v-if="item.id!==isSelect.id" style="position: absolute;right: 10rpx;margin: 0 auto;height: 60rpx;width: 144rpx;border-radius: 67rpx;background-color: #FFEAD4;color: #FF7E32;display: flex;align-items: center;justify-content: center;">
				选择
			</view>
			
			<view class="" @click="unChoose(item)" v-if="item.id==isSelect.id" style="position: absolute;top: 60rpx;right: 10rpx;margin: 0 auto;height: 60rpx;width: 144rpx;border-radius: 67rpx;background-color: #bebdbd;color: #676665;display: flex;align-items: center;justify-content: center;">
				取消
			</view>
		</view>
	</view>
</template>

<script>
import { object } from 'assert-plus';

	export default {
		name:"m-coupon",
		data() {
			return {
				
			};
		},
		props:{
			couponList:{
				type:Array,
				default:[]
			},
			isSelect:{
				type:Object,
				default:null
			},
			shopId:{
				type:String,
				default:''
			},
		},
		watch:{
			isSelect:{
				handler(newVal,oldVal){
					console.log(newVal);
				},
				immediate:true
			}
		},
		methods:{
			choose(e){
				console.log(e);
				this.$emit('chooseCoupon',e)
			},
			unChoose(e){
				console.log(e);
				this.$emit('chooseCoupon','')
			},
		}
	}
</script>

<style>

</style>