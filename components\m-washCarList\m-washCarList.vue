<template>
	<view style="padding: 30rpx;display: flex;flex-direction: column;align-items: center;background-color: #F4F6F8;">
		<view class="" v-for="item in list" @click="toDetail(item)" style="min-height: 285rpx;width: 700rpx;border-radius: 16rpx;background-color: #FFFFFF;
		margin-bottom: 20rpx;padding: 30rpx;position: relative;">
			<view class="" style="display: flex;min-height: 135rpx;width: 640rpx;align-items: center;">
				<image v-if="item.pic" :src="item.pic" style="height: 135rpx;width: 200rpx;border-radius: 16rpx;" mode=""></image>
				<image src="/static/images/noImg.png" v-else style="height: 135rpx;width: 135rpx;" mode=""></image>
				<view class="" style="height: 100%;min-width: 440rpx;padding: 0 0 0 20rpx;">
					<view class=""
						style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
						<view style="color: #00C7A3;font-size: 32rpx;width: 360rpx;">{{item.shopName}}</view>
						<view
							style="font-size: 18rpx;color: #FF7E32;padding:4rpx 4rpx;border-radius: 4rpx;width: fit-content;border: 1px solid #FF7E32;width: 80rpx;display: flex;align-items: center;justify-content: center;">
							{{item.status==1?'营业中':''}}
						</view>
					</view>
					<view style="margin-top: 8rpx;display: flex;align-items: center;">
						<uv-icon name="map" color="#999999" size="14"></uv-icon>
						<text style="font-size: 22rpx;color: #999999;">{{item.address}}</text>
					</view>
					<view class="" style="display: flex;align-items: center;line-height: 50rpx;">
						<uv-icon name="clock" color="#999999" size="14"></uv-icon>
						<text
							style="color: #999999;font-size: 24rpx;">{{item.startTime?item.startTime+'-'+item.endTime:'以加油站营业时间为准'}}</text>
					</view>
					<view class="" v-for="item1 in item.carWashModeList" style="display: flex;align-items: center;">
						<image src="/static/images/kuai.png" style="height: 28rpx;width: 45rpx;"
							v-if="item1.name.includes('快')" mode=""></image>
						<image src="/static/images/you.png" style="height: 28rpx;width: 45rpx;" v-else mode=""></image>
						<text style="font-size: 28rpx;margin-left: 4rpx;">{{item1.price.toFixed(2)}}</text>
						<text style="font-size:18rpx">元/次</text>
					</view>
				</view>
			</view>
			<view class="" @click.stop="toMap(item)"
				style="position: absolute;bottom: 30rpx;right: 30rpx;width: 180rpx;height: 60rpx;
			border-radius: 69rpx;display: flex;align-items: center;justify-content: center;background-color: #FF7E32;color: #FFFFFF;">
				<image src="/static/images/dh.png" style="width: 24rpx;height: 24rpx;" mode=""></image>
				<text style="font-size: 32rpx;font-weight: 600;color: #FFFFFF;margin-left: 10rpx;">去洗车</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-washCarList",
		data() {
			return {

			};
		},
		props: {
			list: {
				type: Array,
				default: []
			}

		},

		computed: {
			...mapState('login', ['userInfo'])
		},
		methods: {
			...mapActions('login', ['toLogin', 'pushWashList', 'pushWash']),
			toDetail(e) {
				console.log(e,'rre');
				this.pushWash(e)
				uni.navigateTo({
					url: '/pages/washCar/detail/detail'
				})
			},
			toMap(e) {
				console.log('dd',e.shopName,e.address,e.latitude);
				uni.openLocation({
					latitude:Number(e.latitude),
					longitude:Number(e.longitude),
					scale: 18,
					name: e.shopName, // 位置名
					address: e.address ,// 地址的详细说明
					complete:res=>{
						console.log(res);
					}
				})
			}
		},
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">

</style>