import Vue from 'vue'

const state = {
	userInfo: {},
	cities: [],
	auth: [],
	shopList: [],
	shop: {},
	carList: '',
	car: '',
	pNum: '',
	orderDetail: null,
	washList: [],
	wash: null,
	ids: null
}

const getters = {

}

const mutations = {
	PUSHUSERINFO: (state, userInfo) => {
		console.log(state, 'sta');
		state.userInfo = userInfo
	},
	PUSHAUTH: (state, auth) => {
		state.auth = auth
	},
	PUSHSHOPLIST: (state, list) => {
		const obj = state
		obj.shopList = list
	},
	PUSHSHOP: (state, shop) => {
		state.shop = shop

	},
	PUSHWASHLIST: (state, washList) => {
		state.washList = washList

	},
	PUSHWASH: (state, wash) => {
		state.wash = wash

	},
	PUSHCAR: (state, carList) => {
		state.carList = carList
	},
	PUSHCARNUMBER: (state, car) => {
		state.car = car
	},
	PUSHCAR: (state, carList) => {
		state.carList = carList
	},
	PUSHCARNUMBER: (state, car) => {
		state.car = car
	},
	PUSHPNUM: (state, pNum) => {
		state.pNum = pNum
	},

	PUSHORDER: (state, orderDetail) => {
		state.orderDetail = orderDetail
	},
	PUSHIDS: (state, ids) => {
		state.ids = ids
	}
}
const actions = {
	// 登录

	toLogin({
		commit
	}, params) {
		commit('PUSHUSERINFO', params)
	},

	updateUserInfo({
		commit
	}, params) {
		commit('PUSHUSERINFO', params)
	},

	updateAuth({
		commit
	}, params) {
		commit('PUSHAUTH', params)
	},
	pushShop({
		commit
	}, list) {
		commit('PUSHSHOP', list)
	},
	pushCode({
		commit
	}, list) {
		commit('GETSESSION', list)
	},
	pushShopList({
		commit
	}, list) {
		commit('PUSHSHOPLIST', list)
	},
	pushCarList({
		commit
	}, list) {
		commit('PUSHCAR', list)
	},
	pushCar({
		commit
	}, list) {
		commit('PUSHCARNUMBER', list)
	},

	pushPNumer({
		commit
	}, list) {
		commit('PUSHPNUM', list)
	},

	pushOrderDetail({
		commit
	}, list) {
		commit('PUSHORDER', list)
	},
	pushWashList({
		commit
	}, list) {
		commit('PUSHWASHLIST', list)
	},

	pushWash({
		commit
	}, list) {
		commit('PUSHWASH', list)
	},
	pushIds({
		commit
	}, list) {
		commit('PUSHIDS', list)
	},
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}