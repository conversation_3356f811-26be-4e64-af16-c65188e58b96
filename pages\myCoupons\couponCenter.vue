<template>
	<view>
		<uv-sticky bgColor="#fff">
			<uv-tabs :list="list" @click="click" :scrollable='false' lineColor="#ffb800" lineWidth="40"></uv-tabs>
			
		</uv-sticky>
		
		<scroll-view class="scrolls" scroll-y style="height: 95vh;">
			<!-- colors:按钮颜色 couponList:优惠卷列表数据  @onReceive：领取或立即使用按钮事件 -->
			<m-couponCenter v-if="hackReset&&couponList.length>0" :couponList="couponList" @takeCoupon="choose" type="center"></m-couponCenter>
			<uv-empty v-else mode="coupon" icon="/static/images/yhq.png" width="60" height="50" marginTop="80"></uv-empty>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list: [],
				couponList: [],
				params:{
					pageNumber: 1,
					pageSize: 10
				},
				hackReset:true,
				current:0
			}
		},
		async onLoad() {
			await this.$onLaunched;
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			
			this.$iBox.http('getCouponType', this.params)({
				method: 'get'
			}).then(res => {
				let list = []
				res.data.forEach(item=>{
					let a = {
						name:''
					}
					a.name = item.couponTypeName
					list.push(a)
				})
				this.list = list
			})
			
			this.params.pageNumber = 1
			this.getWashCouponList()
		},
		methods: {
			click(e){
				console.log(e);
				this.current = e.index
				if(e.index==0){
					this.params.pageNumber = 1
					this.getWashCouponList()
				}else if(e.index==1){
					this.couponList = []
				}else{
					this.couponList = []
				}
			},
			getWashCouponList(){
				this.$iBox.http('getCarWashCouponList', this.params)({
					method: 'post'
				}).then(res => {
					this.couponList = res.data.list
				})
			},
			choose(e){
				console.log(e);
				if(this.current == 0){
					this.$iBox.http('takeCarWashCoupon', {couponId:e.id})({
						method: 'post'
					}).then(res => {
						uni.showToast({
							icon:'none',
							title:'领取成功'
						})
						this.params.pageNumber = 1
						this.getWashCouponList()
					}).catach(err=>{
						uni.showToast({
							icon:'none',
							duration:1500,
							title:err.message
						})
					})
				}else if(this.current == 1){
					
				}else {
					
				}
				
			}
		}
	}
</script>

<style>

</style>