<template>
	<view>
		<view class="" style="width: 100%;height: 300rpx;display: flex;align-items: center;justify-content: center;flex-direction: column;margin-top: 60rpx;">
			<image src="/static/images/rgicon.png" style="width: 120rpx;height: 140rpx;" mode=""></image>
			<text style="font-size: 44rpx;color: #222222;margin-top: 20rpx;">订单结算中</text>
			<text style="font-size: 28rpx;color: #222222;margin-top: 20rpx;width: 660rpx;">(结算完成将自动前往订单页，若不慎退出此页面，可前往订单页面等待结算完成!)</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: '',
				timeInfo: null,
				detail:null
			}
		},
		async onLoad(options) {
			await this.$onLaunched;
			uni.showLoading({
				title:'订单结算中...'
			})
			this.id = options.id
			let self = this
			this.timeInfo && clearInterval(this.timeInfo)
			this.timeInfo = setInterval((function target() {
				self.getDetail()
				return target
			})(), 5000)
		},
		methods: {
			getDetail() {
				this.$iBox.http('queryOrderInfoById', {
					id: this.id
				})({
					method: 'get'
				}).then(res => {
					this.detail = res.data
					if(res.data.chargeStatus==4){
						uni.hideLoading()
						uni.reLaunch({
							url:'/pages/order/order'
						})
						this.timeInfo && clearInterval(this.timeInfo)
						
					}
				})
			}
		},
		onUnload() {
			this.timeInfo && clearInterval(this.timeInfo)
		}
	}
</script>

<style>
	page {
		background: linear-gradient(180deg, #EAFFF9 0%, #FFFFFF 100%);

	}

	view {
		box-sizing: border-box;
	}

	.charts-box {
		width: 100%;
		height: 300px;
	}
</style>