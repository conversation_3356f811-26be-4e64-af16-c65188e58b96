<template>
	<view class="self-wash-container">
		<!-- 轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :autoplay="true" :interval="3000" :duration="500" :indicator-dots="true" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#00C7A3">
				<swiper-item v-for="item in bannerList" :key="item">
					<image :src="item" mode="aspectFill" class="banner-image"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 快捷功能区域 -->
		<view class="quick-actions">
			<view class="action-item primary-action" @click="scanCode">
				<view class="action-icon scan-icon">
					<uv-icon name="scan" size="30"></uv-icon>
				</view>
				<view class="action-content">
					<text class="action-text">扫码洗车</text>
					<text class="action-desc">快速开始</text>
				</view>
			</view>
			<view class="secondary-actions">
				<view class="action-item secondary-action" @click="toRecharge">
					<view class="action-icon recharge-icon">
						<text class="icon-text">💰</text>
					</view>
					<text class="action-text">充值</text>
				</view>
				<view class="action-item secondary-action" @click="toOrders">
					<view class="action-icon order-icon">
						<text class="icon-text">📋</text>
					</view>
					<text class="action-text">订单</text>
				</view>
				<view class="action-item secondary-action" @click="toBalance">
					<view class="action-icon balance-icon">
						<text class="icon-text">💳</text>
					</view>
					<text class="action-text">余额</text>
				</view>
			</view>
		</view>

		<!-- 用户信息卡片 -->
		<view class="user-info-card" v-if="userInfo.phone">
			<view class="card-background">
				<view class="card-header">
					<view class="user-avatar">
						<image v-if="userInfo.avatarUrl" :src="userInfo.avatarUrl" mode="aspectFill"></image>
						<view v-else class="default-avatar">
							<text class="avatar-text">{{getAvatarText()}}</text>
						</view>
					</view>
					<view class="user-details">
						<text class="user-name">{{userInfo.nickName || '用户'}}</text>
						<text class="user-phone">{{formatPhone(userInfo.phone)}}</text>
					</view>
					
				</view>
				<view class="balance-section">
					<view class="balance-item" @click="toRecharge">
						<view class="balance-icon">
							<text class="icon-text">💰</text>
						</view>
						<view class="balance-content">
							<text class="balance-label">账户余额</text>
							<text class="balance-value">¥{{userBalance}}</text>
						</view>
						<view class="balance-arrow">
							<text class="arrow-text">></text>
						</view>
					</view>
					<view class="balance-divider"></view>
					<view class="balance-item" @click="toRecharge">
						<view class="balance-icon">
							<text class="icon-text">🎫</text>
						</view>
						<view class="balance-content">
							<text class="balance-label">洗车卡</text>
							<text class="balance-value">{{washCardCount}}张</text>
						</view>
						<view class="balance-arrow">
							<text class="arrow-text">></text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 附近洗车店铺 -->
		<view class="shop-section">
			<view class="section-header">
				<text class="section-title">附近洗车店铺</text>
				<view class="refresh-btn" @click="refreshShops">
					<text class="refresh-icon">🔄</text>
				</view>
			</view>

			<scroll-view scroll-y="true" class="shop-list" @scrolltolower="loadMore">
				<!-- 加载状态 -->
				<view class="loading-state" v-if="isLoading && shopList.length === 0">
					<view class="loading-icon">
						<text class="loading-emoji">⏳</text>
					</view>
					<text class="loading-text">正在加载附近店铺...</text>
				</view>
				<view class="shop-item" v-for="item in shopList" :key="item.id" @click="toShopDetail(item)">
					<view class="shop-content">
						<view class="shop-header">
							<view class="shop-title-row">
								<text class="shop-name">{{item.shopName}}</text>
								<view class="distance-badge">
									<text class="distance-value">{{formatDistance(item.distance)}}</text>
								</view>
							</view>
							<view class="shop-tags">
								<text class="tag new-tag" v-if="item.isNew">新客</text>
								<text class="tag self-tag">自助</text>
								<text class="tag wash-tag">洗车</text>
								<text class="tag clean-tag" v-if="item.hasClean">清洁</text>
							</view>
						</view>

						<view class="shop-status-row">
							<view class="status-item">
								<text class="status-icon">🕐</text>
								<text class="status-text">{{item.openTime || '24小时营业'}}</text>
							</view>
							<view class="status-item">
								<text class="status-icon">🚗</text>
								<text class="status-text">{{item.availableDevices}}个工位</text>
								<text class="available-status" :class="item.availableDevices > 0 ? 'available' : 'unavailable'">
									{{item.availableDevices > 0 ? item.availableDevices + '个空闲' : '暂无空闲'}}
								</text>
							</view>
						</view>

						<view class="shop-location">
							<text class="location-text">{{item.address}}</text>
							<text class="price-tag" v-if="item.isPreferred">优价推荐</text>
						</view>
					</view>

					<view class="shop-actions">
						<view class="nav-button" @click.stop="navigateToShop(item)">
							<text class="nav-icon">导航</text>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="!isLoading && shopList.length === 0">
					<view class="empty-icon">
						<text class="empty-emoji">🏪</text>
					</view>
					<text class="empty-title">暂无附近店铺</text>
					<text class="empty-desc">请尝试刷新或检查定位权限</text>
					<view class="empty-action" @click="refreshShops">
						<text class="action-text">重新加载</text>
					</view>
				</view>

				<!-- 加载更多状态 -->
				<view class="load-more" v-if="hasMore && shopList.length > 0">
					<uv-loading-icon :show="loading" mode="circle" size="20"></uv-loading-icon>
					<text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
				</view>

				<view class="no-more" v-if="!hasMore && shopList.length > 0">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</scroll-view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="shopList.length == 0 && !loading">
			<uv-empty mode="list" icon="/static/images/noData.png" text="暂无洗车店铺"></uv-empty>
		</view>

		<!-- 登录弹窗 -->
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>

		<!-- 支付方式选择弹窗 -->
		<uv-popup v-model="showPaymentSelect" mode="bottom" border-radius="16">
			<view class="payment-select-popup">
				<view class="popup-header">
					<text class="popup-title">选择支付方式</text>
					<uv-icon name="close" size="24" color="#999999" @click="showPaymentSelect = false"></uv-icon>
				</view>
				<view class="payment-options">
					<view class="payment-option"
						v-for="item in paymentOptions"
						:key="item.value"
						@click="selectPaymentMethod(item)"
					>
						<view class="option-info">
							<uv-icon :name="item.icon" size="32" :color="item.color"></uv-icon>
							<view class="option-text">
								<text class="option-title">{{item.title}}</text>
								<text class="option-desc">{{item.desc}}</text>
							</view>
						</view>
						<uv-icon name="arrow-right" size="20" color="#CCCCCC"></uv-icon>
					</view>
				</view>
			</view>
		</uv-popup>



		<!-- 底部导航占位 -->
		<view class="bottom-placeholder"></view>
	</view>
</template>

<script>
import {
	mapState,
	mapGetters,
	mapActions
} from 'vuex';
import PaymentService from './services/paymentService.js';
import authMixin from './mixins/authMixin.js';

export default {
	mixins: [authMixin],
	data() {
		return {
			bannerList: [
				'/static/images/banner1.jpg',
				'/static/images/banner2.jpg'
			],
			userBalance: '0.00',
			washCardCount: 0,
			paymentService: null,
			shopList: [],
			loading: false,
			isLoading: false,
			hasMore: true,
			showLogin: false,
			showPaymentSelect: false,
			paymentOptions: [],
			currentDeviceId: '',
			hackReset: false,
			if_login: false,
			params: {
				pageNumber: 1,
				pageSize: 10,
				longitude: null,
				latitude: null,
				sortBy: 'distance'
			},
			navBarHeight: 0,
			statusBarHeight: 0
		}
	},
	computed: {
		...mapState('login', ['userInfo']),
	},
	onReady() {
		const systemInfo = wx.getSystemInfoSync();
		this.navBarHeight = systemInfo.statusBarHeight + 44;
		this.statusBarHeight = systemInfo.statusBarHeight;
	},
	async onShow() {
		await this.$onLaunched;
		this.checkLogin();
		this.loadBanners();
		this.loadShopList();
		if (this.userInfo.phone) {
			this.loadUserBalance();
		}
	},
	methods: {
		...mapActions('login', ['toLogin']),
		
		// 格式化手机号
		formatPhone(phone) {
			if (!phone) return '';
			return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
		},
		
		// 拨打电话
		callPhone() {
			if (this.userInfo.phone) {
				uni.makePhoneCall({
					phoneNumber: this.userInfo.phone
				});
			}
		},
		
		// 检查登录状态
		checkLogin() {
			if (!this.userInfo.phone) {
				this.hackReset = true;
				this.if_login = true;
				return false;
			}
			return true;
		},
		
		// 登录成功回调
		loginSucess() {
			this.hackReset = false;
			this.if_login = false;
			this.loadUserBalance();
		},
		
		// 加载轮播图
		loadBanners() {
			this.$iBox.http('getMiniProgramSlideshowList', { moduleId: 1 })({
				method: 'get'
			}).then(res => {
				if (res.data && res.data.length > 0) {
					this.bannerList = res.data.map(item => item.pic);
				}
			}).catch(err => {
				console.log('加载轮播图失败', err);
			});
		},
		
		// 加载用户余额
		loadUserBalance() {
			Promise.all([
				this.$iBox.http('getCarWashSelfBalance', {})({
					method: 'get'
				}),
				this.getUserWashCards()
			]).then(([balanceRes, washCards]) => {
				this.userBalance = balanceRes.data.balance || '0.00';
				this.washCardCount = washCards ? washCards.length : 0;
			}).catch(err => {
				console.log('加载用户余额失败', err);
			});
		},
		
		// 获取用户洗车卡
		getUserWashCards() {
			return this.$iBox.http('getUserOnceWashCardList', {
				pageNumber: 1,
				pageSize: 10
			})({
				method: 'post'
			}).then(res => res.data.list);
		},
		
		// 加载店铺列表
		loadShopList() {
			if (this.loading) return;

			this.loading = true;
			// 首次加载时显示加载状态
			if (this.params.pageNumber === 1) {
				this.isLoading = true;
			}
			
			wx.getLocation({
				success: res => {
					this.params.latitude = res.latitude;
					this.params.longitude = res.longitude;
					
					this.$iBox.http('getCarWashShopList', this.params)({
						method: 'post'
					}).then(res => {
						const shopData = res.data.list.map(item => ({
							...item,
							isNew: Math.random() > 0.7, // 模拟新客标签
							hasClean: Math.random() > 0.5, // 模拟清洁服务
							isPreferred: Math.random() > 0.8, // 模拟优价推荐
							availableDevices: Math.floor(Math.random() * 5) + 1, // 模拟可用设备数
							openTime: '00:00 - 23:59'
						}));
						
						if (this.params.pageNumber == 1) {
							this.shopList = shopData;
						} else {
							this.shopList = this.shopList.concat(shopData);
						}
						
						this.hasMore = this.shopList.length < res.data.total;
						this.loading = false;
						this.isLoading = false;
					}).catch(err => {
						this.loading = false;
						this.isLoading = false;
						console.log('加载店铺列表失败', err);
					});
				},
				fail: err => {
					// 获取位置失败，使用默认参数请求
					this.$iBox.http('getCarWashShopList', this.params)({
						method: 'post'
					}).then(res => {
						if (this.params.pageNumber == 1) {
							this.shopList = res.data.list;
						} else {
							this.shopList = this.shopList.concat(res.data.list);
						}
						
						this.hasMore = this.shopList.length < res.data.total;
						this.loading = false;
						this.isLoading = false;
					}).catch(err => {
						this.loading = false;
						this.isLoading = false;
						console.log('加载店铺列表失败', err);
					});
				}
			});
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return;
			
			this.params.pageNumber++;
			this.loadShopList();
		},
		
		// 跳转到充值页面
		toRecharge() {
			if (!this.checkLogin()) return;
			// 传递店铺ID，可以从当前页面参数或默认值获取
			const shopId = 1; // 默认店铺ID，可以根据实际需求修改
			uni.navigateTo({
				url: `/pages/selfWash/recharge/recharge?shopId=${shopId}`
			});
		},
		
		// 扫码洗车
		scanCode() {
			if (!this.checkLogin()) return;

			uni.scanCode({
				onlyFromCamera: true,
				success: (res) => {
					this.handleScanResult(res);
				},
				fail: (err) => {
					uni.showToast({
						title: '扫码失败，请重试',
						icon: 'none'
					});
				}
			});
		},
		
		// 处理扫码结果
		handleScanResult(scanResult) {
			let deviceId = '';
			
			if (scanResult.result.includes('deviceId=')) {
				deviceId = scanResult.result.split('deviceId=')[1].split('&')[0];
			}

			if (deviceId) {
				this.showPaymentSelection(deviceId);
			} else {
				uni.showToast({
					title: '无效的二维码',
					icon: 'none'
				});
			}
		},
		
		// 显示支付方式选择
		showPaymentSelection(deviceId) {
			this.currentDeviceId = deviceId;

			Promise.all([
				this.$iBox.http('getCarWashSelfBalance', {})({ method: 'get' }),
				this.getUserWashCards()
			]).then(([balanceInfo, washCards]) => {
				const hasBalance = parseFloat(balanceInfo.data.balance || 0) > 0;
				const hasWashCard = washCards && washCards.length > 0;

				this.paymentOptions = [];

				if (hasBalance) {
					this.paymentOptions.push({
						title: '余额支付',
						desc: `可用余额 ¥${balanceInfo.data.balance}`,
						icon: 'wallet',
						color: '#00C7A3',
						value: 'balance'
					});
				}

				if (hasWashCard) {
					this.paymentOptions.push({
						title: '洗车卡支付',
						desc: `${washCards.length}张洗车卡可用`,
						icon: 'card',
						color: '#FF7E32',
						value: 'washcard'
					});
				}

				if (this.paymentOptions.length === 0) {
					uni.showModal({
						title: '提示',
						content: '暂无可用支付方式，请先充值或购买洗车卡',
						confirmText: '去充值',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								this.toRecharge();
							}
						}
					});
				} else if (this.paymentOptions.length === 1) {
					const payType = this.paymentOptions[0].value === 'balance' ? 1 : 3;
					this.navigateToBilling(deviceId, payType);
				} else {
					this.showPaymentSelect = true;
				}
			}).catch(err => {
				console.log('获取支付方式失败', err);
				uni.showToast({
					title: '获取支付信息失败',
					icon: 'none'
				});
			});
		},
		
		// 选择支付方式
		selectPaymentMethod(option) {
			this.showPaymentSelect = false;
			const payType = option.value === 'balance' ? 1 : 3;
			this.navigateToBilling(this.currentDeviceId, payType);
		},
		
		// 跳转到计费页面
		navigateToBilling(deviceId, payType) {
			uni.navigateTo({
				url: `/pages/selfWash/billing/billing?deviceId=${deviceId}&payType=${payType}`
			});
		},
		
		// 跳转到订单页面
		toOrders() {
			if (!this.checkLogin()) return;
			uni.navigateTo({
				url: '/pages/selfWash/orders/orders'
			});
		},

		// 跳转到余额页面
		toBalance() {
			if (!this.checkLogin()) return;
			uni.navigateTo({
				url: '/pages/balanceRecord/balanceRecord'
			});
		},

		// 格式化距离显示
		formatDistance(distance) {
			if (!distance && distance !== 0) return '未知';

			const dist = parseFloat(distance);
			if (isNaN(dist)) return '未知';

			// 小于1km时显示为米
			if (dist < 1) {
				const meters = Math.round(dist * 1000);
				return `${meters}m`;
			} else {
				// 大于等于1km时显示为公里，保留2位小数
				return `${dist.toFixed(2)}km`;
			}
		},

		// 刷新店铺列表
		refreshShops() {
			this.shopList = [];
			this.currentPage = 1;
			this.hasMore = true;
			this.loadShopList();
		},

		// 获取头像文字
		getAvatarText() {
			if (this.userInfo.nickName) {
				// 如果有昵称，取第一个字符
				return this.userInfo.nickName.charAt(0);
			} else if (this.userInfo.phone) {
				// 如果没有昵称但有手机号，取手机号后两位
				return this.userInfo.phone.slice(-2);
			} else {
				// 都没有就显示默认
				return '用';
			}
		},
		
		// 跳转到店铺详情
		toShopDetail(shop) {
			uni.navigateTo({
				url: `/pages/selfWash/shopDetail/shopDetail?shopId=${shop.id}`
			});
		},
		
		// 导航到店铺
		navigateToShop(shop) {
			if (shop.latitude && shop.longitude) {
				wx.openLocation({
					latitude: shop.latitude,
					longitude: shop.longitude,
					name: shop.shopName,
					address: shop.address
				});
			} else {
				uni.showToast({
					title: '暂无位置信息',
					icon: 'none'
				});
			}
		}
	}
}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss">
page {
	background: #F4F6F8;
}

.self-wash-container {
	min-height: 100vh;
	
	.custom-navbar {
		background: linear-gradient(135deg, #00C7A3 0%, #00A693 100%);
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		box-shadow: 0 2rpx 16rpx rgba(0, 199, 163, 0.2);

		.navbar-content {
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 32rpx;

			.navbar-title {
				font-size: 36rpx;
				font-weight: 600;
				color: #FFFFFF;
				letter-spacing: 1rpx;
			}

			.navbar-actions {
				display: flex;
				align-items: center;

				.nav-action {
					width: 64rpx;
					height: 64rpx;
					border-radius: 32rpx;
					background: rgba(255, 255, 255, 0.2);
					display: flex;
					align-items: center;
					justify-content: center;
					backdrop-filter: blur(10rpx);

					&:active {
						transform: scale(0.95);
					}
				}
			}
		}
	}
	
	.banner-section {
		padding: 20rpx 24rpx;
		
		.banner-swiper {
			height: 280rpx;
			border-radius: 16rpx;
			overflow: hidden;
			
			.banner-image {
				width: 100%;
				height: 100%;
			}
		}
	}
	
	.quick-actions {
		padding: 40rpx 32rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.primary-action {
			background: linear-gradient(135deg, #00C7A3 0%, #00A693 100%);
			border-radius: 24rpx;
			padding: 32rpx;
			display: flex;
			align-items: center;
			box-shadow: 0 8rpx 24rpx rgba(0, 199, 163, 0.3);

			.action-icon {
				width: 80rpx;
				height: 80rpx;
				border-radius: 40rpx;
				background: rgba(255, 255, 255, 0.2);
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;

				.icon-text {
					font-size: 36rpx;
				}
			}

			.action-content {
				flex: 1;

				.action-text {
					font-size: 32rpx;
					font-weight: 600;
					color: #FFFFFF;
					margin-bottom: 8rpx;
					display: block;
				}

				.action-desc {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.8);
				}
			}

			&:active {
				transform: scale(0.98);
			}
		}

		.secondary-actions {
			display: flex;
			gap: 16rpx;

			.secondary-action {
				flex: 1;
				background: #FFFFFF;
				border-radius: 16rpx;
				padding: 24rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

				.action-icon {
					width: 64rpx;
					height: 64rpx;
					border-radius: 32rpx;
					background: #F8F9FA;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 16rpx;

					.icon-text {
						font-size: 24rpx;
					}

					&.recharge-icon {
						background: rgba(0, 199, 163, 0.1);
					}

					&.order-icon {
						background: rgba(255, 126, 50, 0.1);
					}

					&.balance-icon {
						background: rgba(24, 144, 255, 0.1);
					}
				}

				.action-text {
					font-size: 24rpx;
					color: #333333;
					font-weight: 500;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}
	
	.user-info-card {
		margin: 0 32rpx 40rpx;
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.3), 0 4rpx 16rpx rgba(118, 75, 162, 0.2);
		position: relative;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			border-radius: 24rpx;
			padding: 2rpx;
			background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, transparent 50%, rgba(255,255,255,0.2) 100%);
			mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
			mask-composite: exclude;
		}

		.card-background {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			padding: 32rpx;
			position: relative;
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: -50%;
				left: -50%;
				width: 200%;
				height: 200%;
				background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%);
				animation: shimmer 4s ease-in-out infinite;
			}

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.05) 100%);
				pointer-events: none;
			}

			.card-header {
				display: flex;
				align-items: center;
				margin-bottom: 32rpx;

				.user-avatar {
					width: 96rpx;
					height: 96rpx;
					border-radius: 48rpx;
					overflow: hidden;
					margin-right: 24rpx;
					border: 4rpx solid rgba(255, 255, 255, 0.4);
					box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

					image {
						width: 100%;
						height: 100%;
					}

					.default-avatar {
						width: 100%;
						height: 100%;
						background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
						display: flex;
						align-items: center;
						justify-content: center;
						backdrop-filter: blur(10rpx);

						.avatar-text {
							font-size: 36rpx;
							font-weight: 600;
							color: #FFFFFF;
							text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
						}
					}
				}

				.user-details {
					flex: 1;

					.user-name {
						display: block;
						font-size: 36rpx;
						font-weight: 600;
						color: #FFFFFF;
						margin-bottom: 8rpx;
						text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
					}

					.user-phone {
						font-size: 26rpx;
						color: rgba(255, 255, 255, 0.9);
						text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
					}
				}

				.vip-badge {
					background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					box-shadow: 0 2rpx 8rpx rgba(240, 147, 251, 0.4);
					border: 1rpx solid rgba(255, 255, 255, 0.4);

					.vip-text {
						font-size: 20rpx;
						color: #FFFFFF;
						font-weight: 600;
						text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
					}
				}
			}

			.balance-section {
				display: flex;
				background: rgba(255, 255, 255, 0.9);
				border-radius: 16rpx;
				padding: 24rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
				backdrop-filter: blur(15rpx);
				border: 1rpx solid rgba(255, 255, 255, 0.3);

				.balance-item {
					flex: 1;
					display: flex;
					align-items: center;
					cursor: pointer;
					transition: all 0.2s ease;

					&:active {
						transform: scale(0.98);
						opacity: 0.8;
					}

					.balance-icon {
						width: 48rpx;
						height: 48rpx;
						border-radius: 24rpx;
						background: rgba(255, 255, 255, 0.2);
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 16rpx;

						.icon-text {
							font-size: 20rpx;
						}
					}

					.balance-content {
						flex: 1;

						.balance-label {
							display: block;
							font-size: 24rpx;
							color: #333333;
							margin-bottom: 4rpx;
						}

						.balance-value {
							font-size: 28rpx;
							font-weight: 600;
							color: #333333;
						}
					}

					.balance-arrow {
						margin-left: 8rpx;

						.arrow-text {
							font-size: 24rpx;
							color: #999999;
							font-weight: 600;
						}
					}
				}

				.balance-divider {
					width: 1rpx;
					height: 60rpx;
					background: #F0F0F0;
					margin: 0 24rpx;
				}
			}
		}
	}
	
	.shop-section {
		flex: 1;

		.section-header {
			padding: 0 32rpx 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.section-title {
				font-size: 36rpx;
				font-weight: 600;
				color: #222222;
			}

			.refresh-btn {
				width: 64rpx;
				height: 64rpx;
				border-radius: 32rpx;
				background: #F8F9FA;
				display: flex;
				align-items: center;
				justify-content: center;

				.refresh-icon {
					font-size: 20rpx;
				}

				&:active {
					transform: scale(0.9);
					background: #E9ECEF;
				}
			}
		}

		.shop-list {
			min-height: 50vh;
			// padding: 0 24rpx;

			.loading-state {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 80rpx 0;

				.loading-icon {
					margin-bottom: 24rpx;
					animation: rotate 1s linear infinite;

					.loading-emoji {
						font-size: 32rpx;
					}
				}

				.loading-text {
					font-size: 28rpx;
					color: #666666;
				}
			}

			.empty-state {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 80rpx 0;

				.empty-icon {
					margin-bottom: 32rpx;
					opacity: 0.6;

					.empty-emoji {
						font-size: 64rpx;
					}
				}

				.empty-title {
					font-size: 32rpx;
					color: #333333;
					font-weight: 600;
					margin-bottom: 16rpx;
				}

				.empty-desc {
					font-size: 26rpx;
					color: #999999;
					margin-bottom: 40rpx;
					text-align: center;
					line-height: 1.5;
				}

				.empty-action {
					background: linear-gradient(135deg, #00C7A3 0%, #00A693 100%);
					padding: 20rpx 40rpx;
					border-radius: 40rpx;

					.action-text {
						font-size: 28rpx;
						color: #FFFFFF;
						font-weight: 600;
					}

					&:active {
						transform: scale(0.95);
					}
				}
			}

			.load-more {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 40rpx 0;
				gap: 16rpx;

				.load-text {
					font-size: 26rpx;
					color: #999999;
				}
			}

			.no-more {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 40rpx 0;

				.no-more-text {
					font-size: 26rpx;
					color: #CCCCCC;
				}
			}

			.shop-item {
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 20rpx;
				margin: 0 8rpx 20rpx 8rpx;
				display: flex;
				align-items: stretch;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
				border: 1rpx solid #F0F0F0;
				transition: all 0.2s ease;
				min-width: 0; // 防止内容溢出

				&:active {
					transform: scale(0.98);
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
				}

				.shop-content {
					flex: 1;
					display: flex;
					flex-direction: column;
					gap: 16rpx;

					.shop-header {
						.shop-title-row {
							display: flex;
							align-items: center;
							justify-content: space-between;
							margin-bottom: 12rpx;

							.shop-name {
								font-size: 32rpx;
								font-weight: 600;
								color: #222222;
								flex: 1;
								margin-right: 16rpx;
								line-height: 1.2;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
								min-width: 0;
							}

							.distance-badge {
								background: linear-gradient(135deg, #F0FFFE 0%, #E8F8F5 100%);
								padding: 6rpx 12rpx;
								border-radius: 16rpx;
								border: 1rpx solid #00C7A3;
								flex-shrink: 0;
								min-width: 80rpx;
								text-align: center;

								.distance-value {
									font-size: 22rpx;
									color: #00C7A3;
									font-weight: 600;
									white-space: nowrap;
								}
							}
						}

						.shop-tags {
							display: flex;
							gap: 8rpx;
							flex-wrap: wrap;

							.tag {
								padding: 6rpx 12rpx;
								font-size: 20rpx;
								border-radius: 10rpx;
								font-weight: 500;
								white-space: nowrap;

								&.new-tag {
									background: linear-gradient(135deg, #FFE8E8 0%, #FFD6D6 100%);
									color: #FF4757;
								}

								&.self-tag {
									background: linear-gradient(135deg, #E8F5FF 0%, #D6EFFF 100%);
									color: #2E86DE;
								}

								&.wash-tag {
									background: linear-gradient(135deg, #E8F8F5 0%, #D6F4ED 100%);
									color: #00A085;
								}

								&.clean-tag {
									background: linear-gradient(135deg, #FFF4E6 0%, #FFEBCC 100%);
									color: #FF7E32;
								}
							}
						}
					}
					
					.shop-status-row {
						display: flex;
						flex-direction: column;
						gap: 8rpx;

						.status-item {
							display: flex;
							align-items: center;
							gap: 8rpx;

							.status-icon {
								font-size: 16rpx;
							}

							.status-text {
								font-size: 24rpx;
								color: #666666;
							}

							.available-status {
								font-size: 22rpx;
								padding: 4rpx 8rpx;
								border-radius: 8rpx;
								font-weight: 500;
								margin-left: 8rpx;

								&.available {
									color: #00C7A3;
									background: rgba(0, 199, 163, 0.1);
								}

								&.unavailable {
									color: #FF6B6B;
									background: rgba(255, 107, 107, 0.1);
								}
							}
						}
					}

					.shop-location {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.location-text {
							font-size: 24rpx;
							color: #999999;
							flex: 1;
							line-height: 1.4;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							min-width: 0;
						}

						.price-tag {
							font-size: 20rpx;
							color: #FF7E32;
							background: linear-gradient(135deg, #FFF4E6 0%, #FFEBCC 100%);
							padding: 6rpx 12rpx;
							border-radius: 12rpx;
							font-weight: 600;
							margin-left: 16rpx;
							white-space: nowrap;
						}
					}
				}
				
				.shop-actions {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-left: 12rpx;
					flex-shrink: 0;

					.nav-button {
						width: 64rpx;
						height: 64rpx;
						background: linear-gradient(135deg, #F0FFFE 0%, #E8F8F5 100%);
						border-radius: 32rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border: 2rpx solid rgba(0, 199, 163, 0.2);
						transition: all 0.2s ease;

						.nav-icon {
							font-size: 20rpx;
						}

						&:active {
							transform: scale(0.9);
							background: linear-gradient(135deg, #00C7A3 0%, #00A693 100%);
						}
					}
				}
			}
			
			.load-more, .no-more {
				text-align: center;
				padding: 40rpx 0;
				color: #999999;
				font-size: 24rpx;
				
				.load-text {
					margin-left: 16rpx;
				}
			}
		}
	}
	
	.empty-state {
		padding: 100rpx 0;
	}
	
	.bottom-placeholder {
		height: 120rpx;
	}

	.payment-select-popup {
		padding: 40rpx 30rpx;

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 40rpx;

			.popup-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #222222;
			}
		}

		.payment-options {
			.payment-option {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx 20rpx;
				background: #F8F9FA;
				border-radius: 16rpx;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.option-info {
					display: flex;
					align-items: center;

					.option-text {
						margin-left: 20rpx;

						.option-title {
							display: block;
							font-size: 30rpx;
							font-weight: 600;
							color: #222222;
							margin-bottom: 8rpx;
						}

						.option-desc {
							font-size: 24rpx;
							color: #666666;
						}
					}
				}
			}
		}
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	50% {
		transform: translateX(100%) translateY(100%) rotate(45deg);
	}
	100% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
}
</style>
