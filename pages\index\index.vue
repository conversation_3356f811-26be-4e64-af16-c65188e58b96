<template>
	<view class="waterfall">
		<!-- <uv-notice-bar :text="noticeText" bgColor="#aa0000" v-if="noticeText"></uv-notice-bar> -->
		<view class="" style="height: 26vh;">
			<swiper class="swiper" style="height:100%;width: 100%;" :autoplay="false">
				<swiper-item v-for="item in swiperList">
					<image :src="item" mode="" style="height:100%;width: 100%;"></image>
				</swiper-item>
			</swiper>
		</view>

		<view class="" @click="chooseShop" style="position: absolute;left: 24rpx;width: 460rpx;border-radius: 40rpx; 
		background-color: #FFFFFF;padding: 0 10rpx;display: flex;align-items: center;" :style="{'top':searchBarTop+'px','height':searchBarHeight+'px'}">
			<view class="" style="display: flex;align-items: center;">
				<view
					style="padding-left: 10rpx;color: #222222;width: 260rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;font-size: 26rpx;">
					点击搜索站点
				</view>
			</view>
			<text style="padding: 0 10rpx;color: #D9D9D9;">|</text>
			<view class="" style="color: #999999;display: flex;align-items: center;width: 180rpx;">
				<uv-icon name="search" color="#999999" size="22"></uv-icon>
				<text style="color: #999999;font-size: 24rpx;">搜索站点</text>
			</view>
		</view>

		<view class="itemBox" style="position: relative;z-index: 2;background-color: #FFFFFF;height: 74vh;">
			<view class="titleBox" style="">
				<text style="font-size: 36rpx;color: #222222;">附近充电站</text>
				<view class="" style="display: flex;align-items: center;">
					<view class="btnT" v-for="item in changeBtnList" :key="item.id" @click="changeBtn(item)"
						:style="item.id == changeBtnId?'background: #C8FFEF;':'background: #e6e6e6;color:#999999;'"
						style="">
						{{item.name}}
					</view>
				</view>

			</view>
			<scroll-view scroll-y="true" style="height: 60vh;" @scrolltolower="refreshItem" :scroll-top="topMargin">
				<view class="shopList">
					<m-powerList :list="shopListArr" :source="'index'" :text="name" v-if="hackReset"></m-powerList>
				</view>
				<view class="" style="height: 160rpx;">

				</view>
			</scroll-view>
		</view>

		
		<m-tabbar></m-tabbar>
	</view>
</template>
<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import {
		guid
	} from '@/uni_modules/uv-ui-tools/libs/function/index.js'
	export default {
		data() {
			return {
				noticeText: '',
				swiperList: [],
				changeBtnList: [{
						id: 1,
						name: '距离最近'
					},
					{
						id: 2,
						name: '价格最低'
					},
					{
						id: 3,
						name: '空闲最多'
					}
				],
				bool: true,
				shopListArr: [],
				changeBtnId: '',
				hackReset: false,
				if_login: false,
				params: {
					pageNumber: 1,
					pageSize: 10,
					longitude: null,
					latitude: null,
					sortBy: 'distance'
				},
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				statusBarHeight: 0,
				city: null,
				name: '',
				topMargin: 0
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop']),
		},
		onReady() {
			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			this.statusBarHeight = systemInfo.statusBarHeight
			console.log(menuButtonInfo,this.statusBarHeight);
		},
		async onShow() {
			await this.$onLaunched;
			this.changeBtnId = this.changeBtnList[0].id
			this.hackReset = false
			this.params.pageNumber = 1
			this.bool = true
			
			
			
			this.$nextTick(() => {
				this.hackReset = true
				console.log(this.userInfo, 'this.userInfo');
				if (this.userInfo.phone) {
					this.if_login = false

				} else {
					this.if_login = true
				}
			})

			uni.showLoading({
				title: '加载中...'
			})
			this.$iBox.http('getMiniProgramNotice', {})({
				method: 'get'
			}).then(res => {
				this.noticeText = res.data.content
			})

			this.$iBox.http('getMiniProgramSlideshowList', {moduleId:3})({
				method: 'get'
			}).then(res => {
				let list = []
				res.data.forEach(item => {
					list.push(item.pic)
				})
				this.swiperList = list
				uni.hideLoading()
			})
			this.getWashShopList()

		},
		methods: {
			...mapActions('login', ['toLogin', 'pushShopList', 'pushShop']),
			changeBtn(e) {
				this.changeBtnId = e.id
				this.name = e.name
				this.bool = true
				this.params.pageNumber = 1
				setTimeout(() => {
					this.topMargin = 0
				}, 500)
				console.log(e);
				if (e.id == 1) {
					this.getWashShopList()
				} else if (e.id == 2) {
					this.getWashShopList()
				} else {
					this.getWashShopList()
				}
			},
			chooseShop() {
				uni.navigateTo({
					url: '/pages/searchPage/searchPage?type=charge'
				})
			},
			payWash() {
				console.log('ddd');
				uni.navigateTo({
					url: '/pages/payWash/payWash'
				})
			},
			toPark() {
				uni.navigateTo({
					url: '/pages/parkMap/parkMap'
				})
			},
			loginSucess() {

				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false

					} else {
						this.if_login = true
					}
				})
			},
			toCharge() {
				uni.navigateTo({
					url: '/pages/chargePark/chargePark'
				})
			},
			toMap1(e) {
				console.log(e, 'gg');
				wx.getLocation({
					type: 'wgs84',
					success: (res) => {
						wx.openLocation({ //​使用微信内置地图查看位置。
							latitude: Number(e.latitude), //要去的纬度-地址
							longitude: Number(e.longitude), //要去的经度-地址
							name: e.parkName,
							address: e.address
						})
					}
				})
			},
			refreshItem(e) {
				console.log(e);
			},
			toMap(e) {
				console.log(e, 'gg');
				wx.getLocation({
					type: 'wgs84',
					success: (res) => {
						wx.openLocation({ //​使用微信内置地图查看位置。
							latitude: Number(e.latitude), //要去的纬度-地址
							longitude: Number(e.longitude), //要去的经度-地址
							name: e.parkingLotName,
							address: e.address
						})
					}
				})
			},
			getWashShopList() {
				// 第一次调起授权， 成功后拉起微信收货地址页面
				uni.showLoading({
					title: '加载中...'
				})

				wx.getLocation({
					success: res => {

						this.params.latitude = res.latitude
						this.params.longitude = res.longitude
						if (this.changeBtnId == 1) {
							this.params.sortBy = 'distance'
							this.$iBox
								.http('getChargingPileParkList', this.params)({
									method: 'post'
								}).then(res => {
									this.shopListArr = res.data.list
									setTimeout(() => {
										this.topMargin = 0.1
									}, 500)
									this.pushShopList(res.data.list);
									uni.hideLoading()

								});
						} else if (this.changeBtnId == 2) {
							this.params.sortBy = 'price'
							this.$iBox
								.http('getChargingPileParkList', this.params)({
									method: 'post'
								}).then(res => {
									uni.hideLoading()
									this.shopListArr = res.data.list
									setTimeout(() => {
										this.topMargin = 0.1
									}, 500)
								});
						} else {
							this.params.sortBy = 'freeCount'
							this.$iBox
								.http('getChargingPileParkList', this.params)({
									method: 'post'
								}).then(res => {
									uni.hideLoading()
									this.shopListArr = res.data.list
									setTimeout(() => {
										this.topMargin = 0.1
									}, 500)
								});
						}
					},
					fail: err => {
						wx.getSetting({
							success: res2 => {
								uni.hideLoading()
								if (!res2.authSetting['scope.userLocation']) {
									wx.authorize({
										scope: 'scope.userLocation',
										success: () => {
											// 第一次调起授权， 成功后拉起微信收货地址页面
											wx.getLocation({
												success: res => {
													this.params.latitude =
														res.latitude
													this.params.longitude =
														res.longitude
													if (this.changeBtnId ==
														1) {
														this.params
															.pageNumber = 1
														this.params
															.sortBy =
															'distance'
														this.$iBox.http(
															'getChargingPileParkList',
															this.params
														)({
															method: 'post'
														}).then(
															res => {
																this.shopListArr =
																	res
																	.data
																	.list
																this.pushShopList(
																	res
																	.data
																	.list
																);
															});
													} else if (this
														.changeBtnId == 2
													) {
														this.params
															.sortBy =
															'price'
														this.params
															.pageNumber = 1
														this.$iBox.http(
															'getChargingPileParkList',
															this.params
														)({
															method: 'post'
														}).then(
															res => {
																this.shopListArr =
																	res
																	.data
																	.list
															});
													} else {
														this.params
															.pageNumber = 1
														this.params
															.sortBy =
															'freeCount'
														this.$iBox
															.http(
																'getChargingPileParkList',
																this
																.params)({
																method: 'post'
															}).then(
																res => {
																	this.shopListArr =
																		res
																		.data
																		.list
																});
													}
												}
											});
										},
										fail: () => {
											if (this.changeBtnId ==
												1) {
												this.params
													.pageNumber = 1
												this.params
													.sortBy =
													'distance'
												this.$iBox.http(
													'getChargingPileParkList',
													this.params
												)({
													method: 'post'
												}).then(
													res => {
														this.shopListArr =
															res
															.data
															.list
														this.pushShopList(
															res
															.data
															.list
														);
													});
											} else if (this
												.changeBtnId == 2
											) {
												this.params
													.sortBy =
													'price'
												this.params
													.pageNumber = 1
												this.$iBox.http(
													'getChargingPileParkList',
													this.params
												)({
													method: 'post'
												}).then(
													res => {
														this.shopListArr =
															res
															.data
															.list
													});
											} else {
												this.params
													.pageNumber = 1
												this.params
													.sortBy =
													'freeCount'
												this.$iBox
													.http(
														'getChargingPileParkList',
														this
														.params)({
														method: 'post'
													}).then(
														res => {
															this.shopListArr =
																res
																.data
																.list
														});
											}
										}
									});
								} else {
									//第一次调起授权， 成功后拉起微信收货地址页面
									wx.getLocation({
										success: res => {
											this.params.latitude = res.latitude
											this.params.longitude = res.longitude
											if (this.changeBtnId == 1) {
												this.params.pageNumber = 1
												this.params.sortBy = 'distance'
												this.$iBox
													.http('getChargingPileParkList',
														this
														.params)({
														method: 'post'
													}).then(res => {
														this.shopListArr = res.data
															.list
														this.pushShopList(res.data
															.list);


													});
											} else if (this.changeBtnId == 2) {
												this.params.pageNumber = 1
												this.params.sortBy = 'price'
												this.$iBox
													.http('getChargingPileParkList',
														this
														.params)({
														method: 'post'
													}).then(res => {
														this.shopListArr = res.data
															.list
													});
											} else {
												this.params.pageNumber = 1
												this.params.sortBy = 'freeCount'
												this.$iBox
													.http('getChargingPileParkList',
														this.params)({
														method: 'post'
													}).then(res => {
														this.shopListArr = res.data
															.list
													});
											}
										}
									});
								}
							}
						});
					}
				});
			},
			refreshItem() {
				console.log(this.bool, 'this.bool');
				if (this.bool) {
					console.log(this.changeBtnId, 'this.changeBtnId');
					if (this.changeBtnId == 1) {
						++this.params.pageNumber
						this.status = 'loadmore'

						uni.showLoading({
							title: '加载中...'
						})
						this.$iBox.http('getChargingPileParkList', this.params)({
							method: 'post'
						}).then(res => {

							let new_list = this.shopListArr.concat(res.data.list)
							this.shopListArr = new_list
							if (this.shopListArr.length == res.data.count) {
								this.bool = false
								this.status = 'nomore'
							}
							uni.hideLoading()
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					} else if (this.changeBtnId == 2) {
						++this.params.pageNumber
						this.status = 'loadmore'

						uni.showLoading({
							title: '加载中...'
						})
						this.$iBox.http('getChargingPileParkList', this.params)({
							method: 'post'
						}).then(res => {

							let new_list = this.shopListArr.concat(res.data.list)
							this.shopListArr = new_list
							if (this.shopListArr.length == res.data.count) {
								this.bool = false
								this.status = 'nomore'
							}
							uni.hideLoading()
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					} else {
						++this.params.pageNumber
						this.status = 'loadmore'

						uni.showLoading({
							title: '加载中...'
						})
						this.$iBox.http('getChargingPileParkList', this.params)({
							method: 'post'
						}).then(res => {

							let new_list = this.shopListArr.concat(res.data.list)
							this.shopListArr = new_list
							if (this.shopListArr.length == res.data.count) {
								this.bool = false
								this.status = 'nomore'
							}
							uni.hideLoading()
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					}



				}
			}
		},
		//
		// // 上拉加载
		onReachBottom() {


		},
		onShareAppMessage() {

		}
	}
</script>
<style>
	page {
		background: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss">
	$show-lines: 1;
	@import '@/common/demo.scss';
	@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

	.waterfall {
		.itemBox {
			width: 100%;
			margin-top: -30rpx;
			border-top-right-radius: 32rpx;
			border-top-left-radius: 32rpx;
			padding: 24rpx 24rpx;

			.itemBoxInner {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 70rpx;
				position: relative;

				.itemBoxInner_box {

					height: 200rpx;
					width: 340rpx;
					background-color: #e6edf6;
					border-radius: 15rpx;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: space-around;

					.boxStyle {
						height: 200rpx;
						width: 100%;
						padding-left: 20rpx;
						padding-top: 10rpx;
						z-index: 9;
						color: #131313;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;

					}



					.boxStyle_around {
						width: 140rpx;
						height: 140rpx;
						border-radius: 50%;
						background-color: #e6edf6;
						position: absolute;
						margin: 0 auto;
						left: 0;
						right: 0;
						top: -56rpx;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}

			}

			.itemBoxInner1 {
				display: flex;
				align-items: center;
				justify-content: center;

				.itemBoxInner1_box {
					height: 200rpx;
					width: 710rpx;
					background-color: #ffb800;
					border-radius: 15rpx;
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #2A2B30;

					.boxStyle {
						height: 100%;
						width: 100%;
						padding-left: 20rpx;
						// padding-top: 40rpx;
						z-index: 9;
						//
						color: #FFFFFF;
						display: flex;
						flex-direction: column;
						//
						justify-content: space-around;
						align-items: flex-start;
					}
				}

			}
		}
	}

	.waterfall-item {
		overflow: hidden;
		margin-top: 10px;
		border-radius: 6px;
	}




	.titleBox {
		display: flex;
		height: 100rpx;
		align-items: center;
		padding: 0 10rpx;
		justify-content: space-between;

		.btnT {
			width: 112rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8rpx;
			color: #077E6C;
			background-color: #C8FFEF;
			font-size: 22rpx;
			margin-right: 20rpx;

		}

	}
</style>