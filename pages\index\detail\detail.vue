<template>
	<view>
		<view class="" style="padding: 30rpx;">
			<view class="" style="display: flex;align-items: center;padding: 0 24rpx;">
				<image src="/static/images/rgicon.png" style="width: 48rpx;height: 48rpx;" mode=""></image>
				<text style="color: #222222;font-size: 36rpx;margin-left: 8rpx;">{{detail.parkName}}</text>
				<!-- <view style="font-size:28rpx;color: #0095F2;display: flex;align-items: center;margin-left: 20rpx;" @click="toMap">点击导航</view> -->
			</view>
			<view class="" style="margin-top: 14rpx;">
				<view class="" style="display: flex;align-items: center;padding-left: 70rpx;width: 100%;">
					<view class="" v-for="item in detail.equipmentList" style="width: fit-content;padding: 9rpx 12rpx;border-radius: 8rpx;background-color: #EFEFEF;
					display: flex;align-items: center;justify-content: center;font-size: 22rpx;color: #999999;margin-right: 20rpx;">
						{{item.serviceName}}
					</view>
				</view>
			</view>
			<view class=""
				style="width: 100%;height: 144rpx;border-radius: 12rpx;margin: 20rpx 0;display: flex;align-items: center;padding:0 20rpx;">
				<uv-album :urls="detail.imageList"  space="8rpx" maxCount="3"></uv-album>
				<!-- <image v-for="(item, index) in detail.imageList" v-if="index<3" :src="item"
					style="width: 32%;height: 144rpx;border-radius: 12rpx;margin-right: 12rpx;" mode=""></image> -->
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">我的车辆</p>
					
					<view class="" @click="addCar"
						style="color: #999999;font-size: 24rpx;display: flex;align-items: center;">
						<text>车辆管理</text>
						<uv-icon name="arrow-right" size="14" color="#999999"></uv-icon>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;width: 100%;margin-top: 8rpx;">
					<view class="" style="margin-right: 10rpx;width: 32rpx;height: 32rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: #5ec3a5;color: #FFFFFF;font-size: 30rpx;">
						<text style="font-size: 20rpx;">P</text>
					</view>
					<view class="" style="display: flex;align-items: center;margin-right: 20rpx;"
						v-for="item in detail.serviceList">
						<!-- <view class=""
							style="width: 9rpx;height: 9rpx;border-radius: 50%;background-color: #00C7A3;margin-right: 8rpx;">
						</view> -->
						<text style="font-size: 26rpx;color: #999999;">{{item.serviceName}}</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;width: 100%;margin-top: 8rpx;" v-if="detail&&detail.parkServiceList.length>0">
					<view class="" style="margin-right: 10rpx;width: 32rpx;height: 32rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: #fa2729;color: #FFFFFF;font-size: 30rpx;">
						<text style="font-size: 20rpx;">占</text>
					</view>
					
					<view class="" style="display: flex;align-items: center;margin-right: 20rpx;"
						v-for="item in detail.parkServiceList" >
						<!-- <view class=""
							style="width: 9rpx;height: 9rpx;border-radius: 50%;background-color: #00C7A3;margin-right: 8rpx;">
						</view> -->
						<text style="font-size: 26rpx;color: #999999;">{{item.serviceName}}</text>
					</view>
				</view>
				<!-- <p style="color: #FF7E32;font-size: 24rpx;margin-top: 10rpx;">部分站点免费停车，无牌车无法减免</p> -->
				<view class="" style="display: flex;align-items: center;margin-top: 10rpx;flex-wrap: wrap;margin-top: 20rpx;">
					<view class="" v-for="item in carList" @click="chooseCar(item)"
						:style="current==item.id?'background:#C8FFEF;color:#077E6C;':'background:#EFEFEF;color:#999999;'"
						style="width: fit-content;padding: 10rpx;border-radius: 12rpx;height: 64rpx;padding: 12rpx 18rpx;margin-right: 12rpx;">
						<text>{{item.carNumber}}</text>
					</view>
					<view class="" @click="addCar"
						style="height: 64rpx;width: 64rpx;border-radius: 12rpx;background:#EFEFEF;color:#999999;display: flex;align-items: center;justify-content: center;">
						<uv-icon name="plus" size="16" color="#999999"></uv-icon>
					</view>
				</view>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">电桩详情</p>
					<view class="" @click="searchPower"
						style="color: #999999;font-size: 24rpx;display: flex;align-items: center;">
						<text>查看所有电桩</text>
						<uv-icon name="arrow-right" size="14" color="#999999"></uv-icon>
					</view>
				</view>

				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;flex-wrap: wrap;">
					<view class="" v-if="detail.deviceFastCount>0&&detail.deviceSlowCount==0" style="width: 100%;height: 120rpx;border-radius: 16rpx;background: linear-gradient(90.22deg, #FF6910 0.17%, #F49C68 99.8%);
display: flex;align-items: center;position: relative;justify-content: center;">
						<image src="/static/images/iconBg.png"
							style="position: absolute;left: -10rpx;bottom: 0rpx;height: 78rpx;width: 93rpx;" mode="">
						</image>
						<image src="/static/images/kcdz.png" style="height: 46rpx;width: 190rpx;" mode=""></image>
						<view class="" style="display: flex;align-items: center;margin-left: 30rpx;">
							<text style="font-size: 36rpx;color: #FFFFFF;">闲 {{detail.deviceFastFreeCount}}</text>
							<text style="color: #FFFFFF;margin: 0 10rpx;">/</text>
							<text style="font-size: 36rpx;color: #b2b2b2;">{{detail.deviceFastCount}}</text>
						</view>
					</view>
					<view class="" v-if="detail.deviceFastCount==0&&detail.deviceSlowCount>0" style="width: 100%;height: 120rpx;border-radius: 16rpx;background: linear-gradient(90.22deg, #0095F2 0.17%, #7BCCFF 99.8%);
					display: flex;align-items: center;position: relative;justify-content: center;">
						<image src="/static/images/iconBg.png"
							style="position: absolute;left: -10rpx;bottom: 0rpx;height: 78rpx;width: 93rpx;" mode="">
						</image>
						<image src="/static/images/mcdz.png" style="height: 46rpx;width: 190rpx;" mode=""></image>
						<view class="" style="display: flex;align-items: center;margin-left: 30rpx;">
							<text style="font-size: 36rpx;color: #FFFFFF;">闲 {{detail.deviceSlowFreeCount}}</text>
							<text style="color: #FFFFFF;margin: 0 10rpx;">/</text>
							<text style="font-size: 36rpx;color: #b2b2b2;">{{detail.deviceSlowCount}}</text>
						</view>
					</view>
					<view class="" v-if="detail.deviceFastCount>0&&detail.deviceSlowCount>0"
						style="display: flex;width: 100%;align-items: center;justify-content: space-between;">
						<view class="" style="width: 48%;height: 120rpx;border-radius: 16rpx;background: linear-gradient(90.22deg, #0095F2 0.17%, #7BCCFF 99.8%);
					display: flex;align-items: center;position: relative;justify-content: center;flex-direction: column;">
							<image src="/static/images/iconBg.png"
								style="position: absolute;left: -10rpx;bottom: 0rpx;height: 78rpx;width: 93rpx;"
								mode="">
							</image>
							<image src="/static/images/mcdz.png" style="height: 46rpx;width: 190rpx;" mode=""></image>
							<view class=""
								style="display: flex;align-items: center;margin-left: 30rpx;margin-top: 14rpx;">
								<text style="font-size: 32rpx;color: #FFFFFF;">闲 {{detail.deviceSlowFreeCount}}</text>
								<text style="color: #FFFFFF;margin: 0 10rpx;">/</text>
								<text style="font-size: 32rpx;color: #b2b2b2;">{{detail.deviceSlowCount}}</text>
							</view>
						</view>
						<view class="" style="width: 48%;height: 120rpx;border-radius: 16rpx;background:linear-gradient(90.22deg, #FF6910 0.17%, #F49C68 99.8%);
						display: flex;align-items: center;position: relative;justify-content: center;flex-direction: column;">
							<image src="/static/images/iconBg.png"
								style="position: absolute;left: -10rpx;bottom: 0rpx;height: 78rpx;width: 93rpx;"
								mode="">
							</image>
							<image src="/static/images/kcdz.png" style="height: 46rpx;width: 190rpx;" mode=""></image>
							<view class=""
								style="display: flex;align-items: center;margin-left: 30rpx;margin-top: 14rpx;">
								<text style="font-size: 32rpx;color: #FFFFFF;">闲 {{detail.deviceFastFreeCount}}</text>
								<text style="color: #FFFFFF;margin: 0 10rpx;">/</text>
								<text style="font-size: 32rpx;color: #b2b2b2;">{{detail.deviceFastCount}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">费用信息</p>
					<view class="" @click="addPrice"
						style="color: #999999;font-size: 24rpx;display: flex;align-items: center;">
						<text>充电费用时段详情  </text>
						<uv-icon name="arrow-right" size="14" color="#999999"></uv-icon>
					</view>
				</view>
				<view class="" style="display: flex;margin-top: 10rpx;align-items: center;height: 60rpx;">
					<text style="color: #FF7E32;font-size: 28rpx;">￥</text>
					<text
						style="color: #FF7E32;font-size: 38rpx;font-weight: 600;" v-if="detail">{{(detail.equipmentPolicy.elecPrice + detail.equipmentPolicy.sevicePrice).toFixed(4)}}</text>
					<text style="font-size: 24rpx;color: #999AA1;margin-left: 4rpx;">元/度</text>
				</view>
				<view class="" style="display: flex;align-items: center;">
					<view class=""
						style="height: 12rpx;width: 12rpx;border-radius: 50%;background-color: #FF7E32;margin-right: 6rpx;">

					</view>
					<text style="font-size: 24rpx;color: #999999;">根据时间自动调整价格</text>
				</view>
				<view class="" style="display: flex;align-items: center;">
					<view class=""
						style="height: 12rpx;width: 12rpx;border-radius: 50%;background-color: #FF7E32;margin-right: 6rpx;">

					</view>
					<p style="margin-right: 20rpx;"><text style="font-size: 24rpx;color: #999999;">电费<text
								style="color:  #FF7E32;">{{detail.equipmentPolicy.elecPrice}}</text>元/度，服务费<text
								style="color:  #FF7E32;">{{detail.equipmentPolicy.sevicePrice}}</text>元/度</text></p>
								<uv-tags v-if="detail.discountRate>0" :text="'服务费'+detail.discountRate*10+'折'" type="error" size="mini" plain></uv-tags>
				</view>
				
			</view>

		</view>

		<view class=""
			style="position: fixed;bottom: 0;height: 148rpx;width: 100%;display: flex;align-items: center;justify-content: center;background-color: #FFFFFF;">
			<view class=""
				style="background: #00C7A3;width: 680rpx;height: 88rpx;border-radius: 88rpx;display: flex;align-items: center;justify-content: center;"
				@click="toScan">
				<text style="color: #FFFFFF;">扫码充电</text>
			</view>
		</view>
		<uv-popup ref="popupPrice" mode="bottom" round="12" :safeAreaInsetBottom="false">
			<view class="" style="height: 90vh;">
				<scroll-view scroll-y="true" style="height: 90vh;">
					<view class="" style="margin: 30rpx 0;display: flex;align-items: center;justify-content: center;width: 100%">
						<view class="" style="width: 39%;display: flex;align-items: center;justify-content: center;">
							收费时段
						</view>
						<view class="" style="width: 19%;display: flex;align-items: center;justify-content: center;">
							<text style="font-size: 30rpx;">价格</text><text style="font-size: 24rpx;">(元/度)</text>
						</view>
						<view class="" style="width: 1%;display: flex;align-items: center;justify-content: center;">
							=
						</view>
						<view class="" style="width: 20%;display: flex;align-items: center;justify-content: center;">
							电费
						</view>
						<view class="" style="width: 1%;display: flex;align-items: center;justify-content: center;">
							+
						</view>
						<view class="" style="width: 19%;display: flex;align-items: center;justify-content: center;">
							服务费
						</view>
					</view>
					<view class="" style="width: 100%;display: flex;flex-direction: column;align-items: center;">
						<view class="" v-for="item in list1" :style="currentTime.id==item.id?'border:1px solid #FF7E32;':''"
							style="border-radius:16rpx;height: 120rpx;width: 702rpx;background-color: #FFFFFF;display: flex;align-items: center;padding: 0 30rpx;margin-top: 20rpx;position: relative;">
							<view class="" v-if="currentTime.id==item.id" style="position: absolute;top: 0;left: 0;background: linear-gradient(90deg, #F77F0B 0%, #EC3902 102.23%);display: flex;align-items: center;justify-content: center;
							width: fit-content;padding:6rpx;border-top-left-radius: 16rpx;border-bottom-right-radius: 16rpx;color: #FFFFFF;font-size: 20rpx;">
								当前时段
							</view>
							<view class="" 
								style="min-width: 30%;display: flex;align-items: center;justify-content: center;font-size: 28rpx;">
								{{item.startTime.split(':')[0]+':'+item.startTime.split(':')[1]}}
								~
								{{item.endTime.split(':')[0]+':'+item.endTime.split(':')[1]}}
							</view>
							<view class=""
								style="min-width:23%;display: flex;align-items: center;justify-content: flex-end;font-size: 28rpx;color: #FF7E32;font-size: 32rpx;">
								{{(item.elecPrice + item.sevicePrice).toFixed(4)}}
							</view>
							<view class=""
								style="min-width:23%;display: flex;align-items: center;justify-content: flex-end;font-size: 28rpx;color: #FFAF71;font-size: 32rpx;">
								{{item.elecPrice}}
							</view>
							<view class=""
								style="min-width:23%;display: flex;align-items: center;justify-content: flex-end;font-size: 28rpx;color: #FFAF71;font-size: 32rpx;">
								{{item.sevicePrice}}
							</view>
						</view>
					</view>
					<view style="margin-top: 30rpx;margin-left: 40rpx;">
						<text style="font-size: 34rpx;color: #222222;">充电费用说明</text>
					</view>
					<view style="margin-top: 30rpx;margin-left: 40rpx;">
						<p style="font-size: 26rpx;color: #00C7A3;">·电费</p>
						<view style="font-size: 26rpx;color: #999999;margin-top: 14rpx;width: 640rpx;">
							根据电价政策、场站用电类型及充电峰平谷时段而调整，为电网收取</view>
					</view>
					<view style="margin-top: 30rpx;margin-left: 40rpx;">
						<p style="font-size: 26rpx;color: #00C7A3;">·服务费</p>
						<p style="font-size: 26rpx;color: #999999;margin-top: 14rpx">包含了站点运营费、设备维护费、人员费用等</p>
					</view>
					<view style="margin-top: 30rpx;margin-left: 40rpx;">
						<p style="font-size: 26rpx;color: #00C7A3;">·跨时段计费</p>
						<view style="font-size: 26rpx;color: #999999;margin-top: 14rpx;width: 640rpx;">跨不同时段充的电量，订单会自动分段的价格计算，无需手动操作
						</view>
					</view>
				</scroll-view>
					
			</view>
		</uv-popup>
		
		<!--  -->
		<uv-popup ref="popup" mode="center" round="12" :safeAreaInsetBottom="false">
			<view style="width: 700rpx;height: 80vh;position: relative;">
				<view class=""
					style="width: 100%;height: 8vh;display: flex;align-items: center;position: absolute;top: 0;">
					<view class="" v-for="item in powerList" @click="choosePower(item)"
						:style="current1==item.id?'font-size:36rpx;':'background:#FFF6ED;border-radius:24rpx;font-size:28rpx;color:#CCCCCC;'"
						style="display: flex;align-items: center;justify-content: center;width: 50%;height: 100%;">
						{{item.name}}
					</view>
				</view>
				<view class="" style="width: 100%;height: 8vh;">

				</view>
				<scroll-view scroll-y="true" style="height: 72vh;width: 100%;position: relative;"
					@scrolltolower="refreshItem">
					<view class="" style="height: 72vh;width: 100%;">
						<view class="" v-for="item in list"
							style="height: 181rpx;width: 100%;padding: 30rpx;border-bottom: 1px solid #EFEFEF;position: relative;">
							<view class="" style="display: flex;align-items: center;">
								<view class=""
									:style="[2,3,4].includes(item.status)?'background:#C8FFEF;color:#077E6C;':(item.status==1?'background:#DEF0FF;color:#0095F2;':'')"
									style="font-size: 22rpx;width: fit-content;padding:4rpx 8rpx;border-radius: 4rpx;display: flex;align-items: center;justify-content: center;">
									{{filterList(item)}}
								</view>
								<view class=""
									style="font-size: 28rpx;color:#222222;font-weight:600;padding: 10rpx;border-radius: 8rpx;width: fit-content;display: flex;align-items: center;justify-content: center;">
									枪编号:{{item.connectorId}}
								</view>
							</view>
							<view class=""
								style="font-size: 22rpx;color:#222222;font-weight:400;padding: 10rpx;border-radius: 8rpx;width: fit-content;display: flex;align-items: center;justify-content: center;">
								{{item.parkName}}
							</view>
							<view class="" style="display: flex;align-items: center;">
								<view class=""
									style="font-size: 22rpx;color:#222222;font-weight:400;padding: 10rpx;border-radius: 8rpx;width: fit-content;display: flex;align-items: center;justify-content: center;">
									功率：{{item.power}}KW
								</view>
								<view class=""
									style="font-size: 22rpx;color:#222222;font-weight:400;padding: 10rpx;border-radius: 8rpx;width: fit-content;display: flex;align-items: center;justify-content: center;">
									电压：{{item.voltageLowerLimits}}V ~ {{item.voltageUpperLimits}}V
								</view>
							</view>
							<view class="" v-if="item.equipchargestatus"
								style="position: absolute;bottom:10rpx;right: 20rpx;font-size: 22rpx;color: #FF7E32;font-weight: 400;">
								<view class="">
									当前电量:{{item.equipchargestatus.soc}}%
								</view>
								<view class="" style="margin-top: 20rpx;">
									充电时间:{{item.LeftTime}}
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uv-popup>
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				detail: null,
				carList: [],
				carNumber: '',
				current: 0,
				powerList: [{
					id: 1,
					name: '快充电桩'
				}, {
					id: 2,
					name: '慢充电桩'
				}],
				current1: 1,
				params: {
					pageNumber: 1,
					pageSize: 10,
					parkId: '',
					fastDevice: true
				},
				list: [],
				payList: [{
					id: 1,
					type: 'weixin',
					name: '微信支付',
					icon: '../../../static/images/wxpay.png',
					desc: '充值消费后剩余金额将原路退回'
				}, {
					id: 2,
					type: 'vip',
					name: '会员支付',
					icon: '../../../static/images/pay.png',
					desc: ''
				}],
				hackReset: true,
				if_login:false,
				currentTime:'',
				list1:[]
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop']),
		},
		async onShow() {
			await this.$onLaunched;
			this.$nextTick(() => {
				this.hackReset = true
				console.log(this.userInfo, 'this.userInfo');
				if (this.userInfo.phone) {
					this.if_login = false
			
				} else {
					this.if_login = true
				}
			})
			let scene = wx.getEnterOptionsSync()
			console.log(scene, this.shop);
			
			if (scene.query.scene) {
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				if (query.includes("m=") && query.includes("i=")) {
					let id = this.$iBox.linkFormat(query, "i")

					this.$iBox.http('getChargingPileParkInfo', {
						id: id
					})({
						method: 'get'
					}).then(res => {
						this.detail = res.data
						
						let a = this.$moment().unix()
						let today = this.$moment().format('YYYY-MM-DD')
						let currentTime = ''
						this.list1 = this.detail.equipmentPolicyList
						this.detail.equipmentPolicyList.forEach(item => {
							let startTime =today+' '+ item.startTime
							let endTime =today+' ' +item.endTime
							
							let s = this.$moment(startTime, 'YYYY-MM-DD HH:mm:ss').unix()
							let e = this.$moment(endTime, 'YYYY-MM-DD HH:mm:ss').unix()
							if(a<e&&a>s){
								currentTime = item
							}
						
						})
						
						this.currentTime = currentTime
						
						if (this.detail.deviceSlowCount == 0 && this.detail.deviceFastCount > 0) {
							this.powerList = [{
								id: 1,
								name: '快充电桩'
							}]
						} else if (this.detail.deviceSlowCount > 0 && this.detail.deviceFastCount == 0) {
							this.powerList = [{
								id: 1,
								name: '慢充电桩'
							}]
						} else if (this.detail.deviceSlowCount > 0 && this.detail.deviceFastCount > 0) {
							this.powerList = [{
								id: 1,
								name: '快充电桩'
							}, {
								id: 2,
								name: '慢充电桩'
							}]
						}
					})
				}
			} else {
				if (this.shop) {
					this.detail = this.shop
					this.list1 = this.detail.equipmentPolicyList
					let a = this.$moment().unix()
					let today = this.$moment().format('YYYY-MM-DD')
					let currentTime = ''
					this.detail.equipmentPolicyList.forEach(item => {
						let startTime =today+' '+ item.startTime
						let endTime =today+' ' +item.endTime
						
						let s = this.$moment(startTime, 'YYYY-MM-DD HH:mm:ss').unix()
						let e = this.$moment(endTime, 'YYYY-MM-DD HH:mm:ss').unix()
						if(a<e&&a>s){
							currentTime = item
						}
					
					})
					
					this.currentTime = currentTime
					if (this.detail.deviceSlowCount == 0 && this.detail.deviceFastCount > 0) {
						this.powerList = [{
							id: 1,
							name: '快充电桩'
						}]
					} else if (this.detail.deviceSlowCount > 0 && this.detail.deviceFastCount == 0) {
						this.powerList = [{
							id: 1,
							name: '慢充电桩'
						}]
					} else if (this.detail.deviceSlowCount > 0 &&this.detail.deviceFastCount > 0) {
						this.powerList = [{
							id: 1,
							name: '快充电桩'
						}, {
							id: 2,
							name: '慢充电桩'
						}]
					}
				}
			}


			

			this.bool = true
			this.$iBox.http('getUserCar', {

			})({
				method: 'get'
			}).then(res => {
				if (res.data.length > 0) {
					this.carNumber = res.data[0].carNumber
					this.carList = res.data
					this.current = res.data[0].id
				}
			})

		},
		methods: {
			...mapActions('login', ['pushPNumer','pushShop','pushWash']),
			chooseCar(e) {
				this.carNumber = e.carNumber
				this.current = e.id
			},
			toScan() {
				this.$nextTick(() => {
					this.hackReset = true
					console.log(this.userInfo, 'this.userInfo');
					if (this.userInfo.phone) {
						this.if_login = false
						uni.scanCode({
							onlyFromCamera: true,
							success:  (res)=> {
								console.log('条码类型：' + res.scanType);
								console.log('条码内容：' + JSON.stringify(res))
								
								if(res.scanType == 'WX_CODE'){
									let query = decodeURIComponent(res.path.split('?')[1].split('=')[1])
									console.log(query,'qu');
									let id = this.$iBox.linkFormat(query, "i")
									let mode = this.$iBox.linkFormat(query, "m")
									let c = this.$iBox.linkFormat(query, "c")
									if(mode==1){
										this.$iBox.http('getCarWashShop', {id:id})({
											method: 'get'
										}).then(res => {  
											this.pushWash(res.data)
											uni.navigateTo({
												url:'/pages/washCar/detail/detail'
											})
										})
									}else{
										this.pushPNumer(number)
										uni.navigateTo({
											url:'/pages/index/detail/gunDetail'
										})
									}
									
								}else {
									
									if(res.result.includes('https')){
										let number = res.result.split('?')[1].split('=')[1]
										console.log(number,'number');
										this.pushPNumer(number)
										uni.navigateTo({
											url:'/pages/index/detail/gunDetail'
										})
									} else {
										
										let id = res.result.split('?')[1].split('&')[1].split('=')[1]
										if(id){
											this.$iBox.http('getCarWashShop', {id:id})({
												method: 'get'
											}).then(res => {  
												this.pushWash(res.data)
												uni.navigateTo({
													url:'/pages/washCar/detail/detail'
												})
											})
										}
									
										
									}
								}
								
								
							}
						});
					} else {
						this.if_login = true
					}
				})
				
			},
			loginSucess() {
			
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false
			
					} else {
						this.if_login = true
					}
				})
			},
			addCar() {
				uni.navigateTo({
					url: '/pages/addCar/addCar'
				})
			},
			searchPower() {
				this.$refs.popup.open();
				uni.showLoading({
					title: '查询设备中...'
				})
				this.params.pageNumber = 1
				this.params.parkId = this.detail.id
				this.$iBox.http('getEquipmentConnectorList', this.params)({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.list = res.data.list
				})
			},
			addPrice() {
				this.$refs.popupPrice.open()
			},
			filterList(e) {
				//0离线，1空闲，2占用（未充电），3占用（充电中），4占用（预约占用），255故障
				let list = [{
					id: 0,
					name: '离线'
				}, {
					id: 1,
					name: '空闲'
				}, {
					id: 2,
					name: '占用(未充电)'
				}, {
					id: 3,
					name: '占用(充电中)'
				}, {
					id: 4,
					name: '占用(预约占用)'
				}, {
					id: 255,
					name: '故障'
				}]

				let a = list.filter(item => {
					return item.id == e.status
				})[0]
				return a ? a.name : ''
			},
			choosePower(e) {
				this.current1 = e.id
				console.log(e);
				this.params.pageNumber = 1
				uni.showLoading({
					title: '查询设备中...'
				})
				if (e.name.includes('慢')) {
					this.params.parkId = this.detail.id
					this.params.fastDevice = false
					this.$iBox.http('getEquipmentConnectorList', this.params)({
						method: 'post'
					}).then(res => {
						uni.hideLoading()
						this.list = res.data.list
					})
				} else {
					this.params.parkId = this.detail.id
					this.params.fastDevice = true
					this.$iBox.http('getEquipmentConnectorList', this.params)({
						method: 'post'
					}).then(res => {
						uni.hideLoading()
						this.list = res.data.list
					})
				}
			},
			refreshItem() {
				console.log('dd');
				if (this.bool) {
					console.log(this.changeBtnId, 'this.changeBtnId');
					if (this.current1 == 1) {
						++this.params.pageNumber
						this.params.fastDevice = true

						uni.showLoading({
							title: '查询设备中...'
						})
						this.$iBox.http('getEquipmentConnectorList', this.params)({
							method: 'post'
						}).then(res => {

							let new_list = this.list.concat(res.data.list)
							this.list = new_list
							if (this.list.length == res.data.count) {
								this.bool = false
								this.status = 'nomore'
							}
							uni.hideLoading()
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					} else if (this.changeBtnId == 2) {
						++this.params.pageNumber
						this.params.fastDevice = false

						uni.showLoading({
							title: '加载中...'
						})
						this.$iBox.http('getEquipmentConnectorList', this.params)({
							method: 'post'
						}).then(res => {

							let new_list = this.list.concat(res.data.list)
							this.list = new_list
							if (this.list.length == res.data.count) {
								this.bool = false
								this.status = 'nomore'
							}
							uni.hideLoading()
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					}
				}
			}
		},
		onHide() {
			this.pushShop('')
		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">

</style>