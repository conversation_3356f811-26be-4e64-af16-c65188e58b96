<template>
	<view class="balance-record-container">
		<!-- 余额统计 -->
		<view class="balance-summary">
			<view class="summary-item">
				<text class="summary-label">当前余额</text>
				<text class="summary-value">¥{{currentBalance}}</text>
			</view>
			<view class="summary-item">
				<text class="summary-label">累计充值</text>
				<text class="summary-value">¥{{totalRecharge}}</text>
			</view>
			<view class="summary-item">
				<text class="summary-label">累计消费</text>
				<text class="summary-value">¥{{totalConsume}}</text>
			</view>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-tabs">
			<view class="tab-item" 
				v-for="(item, index) in tabList" 
				:key="item.id"
				@click="changeTab(index)"
				:class="currentTab == index ? 'active' : ''"
			>
				{{item.name}}
			</view>
		</view>

		<!-- 自助洗车入口 -->
		<view class="self-wash-entry" @click="goToSelfWash">
			<view class="entry-content">
				<view class="entry-icon">🚗</view>
				<view class="entry-text">
					<text class="entry-title">自助洗车</text>
					<text class="entry-desc">24小时自助服务</text>
				</view>
				<view class="entry-arrow">→</view>
			</view>
		</view>

		<!-- 记录列表 -->
		<scroll-view scroll-y="true" class="record-list" @scrolltolower="loadMore">
			<view class="record-item" v-for="item in recordList" :key="item.id">
				<view class="record-info">
					<text class="record-title">{{item.title}}</text>
					<text class="record-time">{{item.createTime}}</text>
					<text class="record-order" v-if="item.orderNo">订单号：{{item.orderNo}}</text>
				</view>
				<view class="record-amount">
					<text class="amount-text" :class="{ positive: item.amount > 0, negative: item.amount < 0 }">
						{{item.amount > 0 ? '+' : ''}}¥{{Math.abs(item.amount)}}
					</text>
					<text class="balance-text">余额：¥{{item.balance}}</text>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<uv-loading-icon :show="loading" mode="circle" size="20"></uv-loading-icon>
				<text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
			</view>
			
			<!-- 没有更多数据 -->
			<view class="no-more" v-if="!hasMore && recordList.length > 0">
				<text>没有更多数据了</text>
			</view>
		</scroll-view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="recordList.length == 0 && !loading">
			<uv-empty mode="list" icon="/static/images/noData.png" text="暂无记录"></uv-empty>
		</view>

		<!-- 登录弹窗 -->
		<m-login v-if="hackReset&&if_login" @loginTo="loginSuccess"></m-login>
	</view>
</template>

<script>
import {
	mapState,
	mapActions
} from 'vuex';

export default {
	data() {
		return {
			currentBalance: '0.00',
			totalRecharge: '0.00',
			totalConsume: '0.00',
			tabList: [
				{ id: 0, name: '全部' },
				{ id: 1, name: '充值' },
				{ id: 2, name: '消费' }
			],
			currentTab: 0,
			recordList: [],
			loading: false,
			hasMore: true,
			hackReset: false,
			if_login: false,
			params: {
				pageNumber: 1,
				pageSize: 10,
				type: null
			}
		}
	},
	computed: {
		...mapState('login', ['userInfo']),
	},
	async onShow() {
		await this.$onLaunched;
		if (!this.checkLogin()) return;

		this.loadBalanceSummary();
		this.loadRecordList();
	},
	methods: {
		...mapActions('login', ['updateUserInfo']),

		// 跳转到自助洗车页面
		goToSelfWash() {
			uni.navigateTo({
				url: '/pages/selfWash/selfWash'
			});
		},

		// 加载余额统计
		loadBalanceSummary() {
			this.$iBox.http('carWashSelfBalance/getCarWashSelfBalance', {})({
				method: 'get'
			}).then(res => {
				this.currentBalance = res.data.balance || '0.00';
				// 由于接口只返回当前余额，累计充值和消费需要通过记录计算或单独接口获取
				this.calculateSummary();
			}).catch(err => {
				console.log('加载余额统计失败', err);
			});
		},

		// 计算统计数据
		calculateSummary() {
			// 获取所有记录来计算统计数据
			this.$iBox.http('carWashSelfBalance/getCarWashSelfBalanceRecord', {
				pageNumber: 1,
				pageSize: 1000 // 获取足够多的记录用于统计
			})({
				method: 'post'
			}).then(res => {
				const records = res.data.list || [];
				let totalRecharge = 0;
				let totalConsume = 0;

				records.forEach(record => {
					if (record.type === 1) { // 充值
						totalRecharge += parseFloat(record.amount || 0);
					} else if (record.type === 2) { // 消费
						totalConsume += parseFloat(record.amount || 0);
					}
				});

				this.totalRecharge = totalRecharge.toFixed(2);
				this.totalConsume = totalConsume.toFixed(2);
			}).catch(err => {
				console.log('计算统计数据失败', err);
			});
		},
		
		// 切换标签
		changeTab(index) {
			this.currentTab = index;
			this.params.pageNumber = 1;
			this.hasMore = true;
			
			// 设置类型筛选
			if (index == 0) {
				this.params.type = null;
			} else if (index == 1) {
				this.params.type = 1; // 充值
			} else if (index == 2) {
				this.params.type = 2; // 消费
			}
			
			this.loadRecordList();
		},
		
		// 加载记录列表
		loadRecordList() {
			if (this.loading) return;

			this.loading = true;

			this.$iBox.http('getCarWashSelfBalanceRecord', this.params)({
				method: 'post'
			}).then(res => {
				let records = res.data.list || [];

				// 根据类型筛选
				if (this.params.type) {
					records = records.filter(item => item.type === this.params.type);
				}

				// 处理记录数据，添加标题和格式化
				records = records.map(item => {
					return {
						...item,
						title: this.getRecordTitle(item),
						createTime: this.formatDate(item.createTime || new Date().toISOString()),
						// 确保金额和余额是数字
						amount: parseFloat(item.amount || 0),
						balance: parseFloat(item.balance || 0)
					};
				});

				if (this.params.pageNumber == 1) {
					this.recordList = records;
				} else {
					this.recordList = this.recordList.concat(records);
				}

				// 使用接口返回的分页信息
				this.hasMore = res.data.hasNextPage || false;
				this.loading = false;
			}).catch(err => {
				this.loading = false;
				console.log('加载记录列表失败', err);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			});
		},

		// 获取记录标题
		getRecordTitle(record) {
			const typeMap = {
				1: '账户充值',
				2: '洗车消费',
				3: '退款',
				4: '系统赠送',
				5: '系统扣除'
			};
			return typeMap[record.type] || '余额变动';
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return '';
			const date = new Date(dateStr);
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');

			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},

		// 统一的登录检查方法
		checkLogin() {
			if (!this.userInfo.phone) {
				this.hackReset = true;
				this.if_login = true;
				return false;
			}
			return true;
		},

		// 登录成功回调
		loginSuccess() {
			this.hackReset = false;
			this.if_login = false;
			// 刷新数据
			this.loadBalanceSummary();
			this.loadRecordList();
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return;
			
			this.params.pageNumber++;
			this.loadRecordList();
		}
	}
}
</script>

<style lang="scss">
page {
	background: #F4F6F8;
}

.balance-record-container {
	min-height: 100vh;
	
	.balance-summary {
		background: linear-gradient(135deg, #00C7A3 0%, #00A085 100%);
		padding: 40rpx 30rpx;
		display: flex;
		justify-content: space-around;
		color: #FFFFFF;
		
		.summary-item {
			text-align: center;
			
			.summary-label {
				display: block;
				font-size: 24rpx;
				opacity: 0.9;
				margin-bottom: 12rpx;
			}
			
			.summary-value {
				font-size: 32rpx;
				font-weight: 600;
			}
		}
	}
	
	.filter-tabs {
		background: #FFFFFF;
		display: flex;
		padding: 0 24rpx;
		
		.tab-item {
			flex: 1;
			text-align: center;
			padding: 32rpx 0;
			font-size: 28rpx;
			color: #666666;
			position: relative;
			
			&.active {
				color: #00C7A3;
				font-weight: 600;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 4rpx;
					background: #00C7A3;
					border-radius: 2rpx;
				}
			}
		}
	}

	// 自助洗车入口样式
	.self-wash-entry {
		margin: 24rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 16rpx;
		padding: 24rpx;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);

		.entry-content {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.entry-icon {
				width: 60rpx;
				height: 60rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
			}

			.entry-text {
				flex: 1;
				margin-left: 20rpx;
				display: flex;
				flex-direction: column;
				gap: 4rpx;

				.entry-title {
					font-size: 28rpx;
					font-weight: 600;
					color: #FFFFFF;
				}

				.entry-desc {
					font-size: 22rpx;
					color: rgba(255, 255, 255, 0.8);
				}
			}

			.entry-arrow {
				font-size: 24rpx;
				color: #FFFFFF;
				font-weight: 600;
			}
		}

		&:active {
			transform: scale(0.98);
		}
	}

	.record-list {
		height: calc(100vh - 280rpx);
		padding: 24rpx;
		
		.record-item {
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.record-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 8rpx;

				.record-title {
					display: block;
					font-size: 30rpx;
					color: #222222;
					font-weight: 500;
				}

				.record-time {
					font-size: 24rpx;
					color: #999999;
				}

				.record-order {
					font-size: 22rpx;
					color: #CCCCCC;
				}
			}

			.record-amount {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				gap: 8rpx;

				.amount-text {
					font-size: 32rpx;
					font-weight: 600;

					&.positive {
						color: #00C7A3;
					}

					&.negative {
						color: #FF6B6B;
					}
				}

				.balance-text {
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
		
		.load-more, .no-more {
			text-align: center;
			padding: 40rpx 0;
			color: #999999;
			font-size: 24rpx;
			
			.load-text {
				margin-left: 16rpx;
			}
		}
	}
	
	.empty-state {
		padding: 100rpx 0;
	}
}
</style>
