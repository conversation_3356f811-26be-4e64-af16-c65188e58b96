<template>
	<view>
		<view class="" style="padding: 30rpx;">
			<view class="" style="padding: 0 24rpx;font-size: 34rpx;">
				<text style="font-size: 40rpx;font-weight: 600;color: #222222;">{{pNumber?pNumber:''}}号枪</text>
			</view>
			<view class="" style="display: flex;align-items: center;padding: 0 24rpx;margin-top: 10rpx;">
				<image src="/static/images/rgicon.png" style="width: 48rpx;height: 48rpx;" mode=""></image>
				<text style="color: #222222;font-size: 36rpx;margin-left: 8rpx;">{{detail.parkName}}</text>
			</view>
			<view class="">
				<view class="" style="display: flex;align-items: center;margin: 20rpx;padding-left: 64rpx;width: 100%;">
					<view class="" style="display: flex;align-items: center;margin-right: 20rpx;"
						v-for="item in detail.serviceList">
						<view class=""
							style="width: 9rpx;height: 9rpx;border-radius: 50%;background-color: #00C7A3;margin-right: 8rpx;">
						</view>
						<text style="font-size: 26rpx;color: #999999;">{{item.serviceName}}</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;padding-left: 70rpx;width: 100%;">
					<view class="" v-for="item in detail.equipmentList" style="width: fit-content;padding: 9rpx 12rpx;border-radius: 8rpx;background-color: #EFEFEF;
					display: flex;align-items: center;justify-content: center;font-size: 22rpx;color: #999999;margin-right: 20rpx;">
						{{item.serviceName}}
					</view>
				</view>
			</view>
			<view class=""
				style="width: 100%;height: 144rpx;border-radius: 12rpx;margin: 20rpx 0;display: flex;align-items: center;padding:0 20rpx;">
				<image v-for="(item, index) in detail.imageList" v-if="index<3" :src="item"
					style="width: 32%;height: 144rpx;border-radius: 12rpx;margin-right: 12rpx;" mode=""></image>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">我的车辆</p>
					<view class="" @click="addCar"
						style="color: #999999;font-size: 24rpx;display: flex;align-items: center;">
						<text>车辆管理</text>
						<uv-icon name="arrow-right" size="14" color="#999999"></uv-icon>
					</view>
				</view>
				<p style="color: #FF7E32;font-size: 24rpx;margin-top: 10rpx;">部分站点免费停车，无牌车无法减免</p>
				<view class="" style="display: flex;align-items: center;margin-top: 10rpx;flex-wrap: wrap;">
					<view class="" v-for="item in carList" @click="chooseCar(item)"
						:style="current==item.id?'background:#C8FFEF;color:#077E6C;':'background:#EFEFEF;color:#999999;'"
						style="width: fit-content;padding: 10rpx;border-radius: 12rpx;height: 64rpx;padding: 12rpx 18rpx;margin-right: 12rpx;">
						<text>{{item.carNumber}}</text>
					</view>
					<view class="" @click="addCar"
						style="height: 64rpx;width: 64rpx;border-radius: 12rpx;background:#EFEFEF;color:#999999;display: flex;align-items: center;justify-content: center;">
						<uv-icon name="plus" size="16" color="#999999"></uv-icon>

					</view>
				</view>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">电桩详情</p>
				</view>

				<view class="" style="margin-top: 20rpx;flex-wrap: wrap;">
					<view class="" v-if="detail">
						<text style="color: #999999;">状态:</text><text
							:style="filterList(detail)=='空闲'?'color:#00C7A3;':'color:#ff5500;'">{{filterList(detail)}}</text>
					</view>
					<view class="" style="margin-top: 14rpx;">
						<text>功率:{{detail.power}}KW</text>
						<text style="margin-left: 20rpx;">电压:{{detail.voltageLowerLimits}}V
							~{{detail.voltageUpperLimits}}V</text>
					</view>
				</view>
			</view>
			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">费用信息</p>
					<view class="" @click="addPrice"
						style="color: #999999;font-size: 24rpx;display: flex;align-items: center;">
						<text>充电费用时段详情 </text>
						<uv-icon name="arrow-right" size="14" color="#999999"></uv-icon>
					</view>
				</view>
				<view class="" style="display: flex;margin-top: 10rpx;align-items: center;height: 60rpx;">
					<text style="color: #FF7E32;font-size: 28rpx;">￥</text>
					<text
						style="color: #FF7E32;font-size: 38rpx;font-weight: 600;">{{(currentTime.elecPrice + currentTime.sevicePrice).toFixed(4)}}</text>
					<text style="font-size: 24rpx;color: #999AA1;margin-left: 4rpx;">元/度</text>
				</view>
				<view class="" style="display: flex;align-items: center;">
					<view class=""
						style="height: 12rpx;width: 12rpx;border-radius: 50%;background-color: #FF7E32;margin-right: 6rpx;">

					</view>
					<text style="font-size: 24rpx;color: #999999;">根据时间自动调整价格</text>
				</view>
				<view class="" style="display: flex;align-items: center;">
					<view class=""
						style="height: 12rpx;width: 12rpx;border-radius: 50%;background-color: #FF7E32;margin-right: 6rpx;">

					</view>
					<p style="margin-right: 20rpx;"><text style="font-size: 24rpx;color: #999999;">电费<text
								style="color:  #FF7E32;">{{currentTime.elecPrice}}</text>元/度，服务费<text
								style="color:  #FF7E32;">{{currentTime.sevicePrice}}</text>元/度</text></p>
					<uv-tags v-if="detail.discountRate>0" :text="'服务费'+detail.discountRate*10+'折'" type="error"
						size="mini" plain></uv-tags>
				</view>

			</view>

			<view class=""
				style="padding: 20rpx;margin: 700rpx;min-height: 200rpx;margin: 20rpx auto;border-radius: 16rpx;background-color: #FFFFFF;">
				<view class="" style="width: 100%;display: flex;align-items:center;justify-content: space-between;">
					<p style="font-size: 32rpx;font-weight: 600;color: #222222;">支付方式</p>
				</view>
				<view class=""
					style="margin-top: 30rpx;display: flex;align-items: center;justify-content: space-between;"
					@click="choosePay(item)" v-for="item in payList">
					<view class="">
						<view class="" style="display: flex;align-items: center;">
							<image :src="item.icon" style="width: 32rpx;height: 32rpx;" mode=""></image>
							<text style="font-size: 28rpx;font-weight: 600;margin-left: 10rpx;">{{item.name}}</text>
						</view>
						<p style="font-size: 22rpx;color: #FF7E32;margin-left: 46rpx;margin-top: 10rpx;">
							{{item.desc?item.desc:'会员余额剩余'+centerUser.userBalance.balance+'元'}}
						</p>
					</view>
					<view class=""
						style="display: flex;align-items: center;justify-content: center;width: 60rpx;height: 60rpx;">
						<view class="" v-if="payItem!=item.id"
							style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

							<uv-icon name="checkmark" color="#999999" size="16"></uv-icon>
						</view>
						<view class="" v-if="payItem==item.id" :style="payItem==item.id?'background:#C8FFEF;':''"
							style="display: flex;align-items: center;justify-content: center;width: 40rpx;height: 40rpx;border-radius: 50%;border: 1px solid #999999;">

							<uv-icon name="checkmark" color="#077E6C" size="16"></uv-icon>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="" style="height: 160rpx;">

		</view>
		<view class=""
			style="position: fixed;bottom: 0;height: 148rpx;width: 100%;display: flex;align-items: center;justify-content: center;background-color: #FFFFFF;">
			<view class="" @click="startCharge"
				style="background: #00C7A3;width: 680rpx;height: 88rpx;border-radius: 88rpx;display: flex;align-items: center;justify-content: center;">
				<text style="color: #FFFFFF;">开始充电</text>
			</view>
		</view>

		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				detail: null,
				carList: [],
				carNumber: '',
				current: 0,
				powerList: [{
					id: 1,
					name: '快充电桩'
				}, {
					id: 2,
					name: '慢充电桩'
				}],
				current1: 1,
				params: {
					pageNumber: 1,
					pageSize: 10,
					parkId: '',
					fastDevice: true
				},
				list: [],
				payList: [{
					id: 1,
					type: 'weixin',
					name: '微信支付',
					icon: '../../../static/images/wxpay.png',
					desc: '充值消费后剩余金额将原路退回'
				}, {
					id: 2,
					type: 'vip',
					name: '会员支付',
					icon: '../../../static/images/pay.png',
					desc: ''
				}],
				pNumber: '',
				payItem: 1,
				centerUser: null,
				couponList: [],
				hackReset: true,
				if_login: false,
				currentTime: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'shopList', 'shop', 'pNum', 'car']),
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options, 'options');

			if (options.query && options.query.scene) {
				let query = decodeURIComponent(options.query.scene)
				this.pNumber = this.$iBox.linkFormat(query, "c")
				this.pushPNumer(this.pNumber)
			} else if (options.q) {
				let query = decodeURIComponent(options.q)
				console.log(query);
				this.pNumber = query.split('?')[1].split('=')[1]
				this.pushPNumer(this.pNumber)
			}else{
				if(this.pNum){
					this.pNumber = this.pNum
				}
				
			}
			//q: "https%3A%2F%2Fxingshuncl.cn%2Fcharge.aspx%3Fpile%3D4601000004333204", scancode_time: "1753882386"
			//	 "https%3A%2F%2Fxingshuncl.cn%2Fcharge.aspx%3Fpile%3D4601000004333203

		},
		async onShow() {
			await this.$onLaunched;
			this.$nextTick(() => {
				this.hackReset = true
				console.log(this.userInfo, 'this.userInfo');
				if (this.userInfo.phone) {
					this.if_login = false

				} else {
					this.if_login = true
				}
			})

			uni.showLoading({
				title: '加载中...'
			})

			this.$iBox.http('getConnectorInfo', {
				connectorId: this.pNumber
			})({
				method: 'get'
			}).then(res => {

				this.detail = res.data
				if (this.detail.chargingPileOrder) {
					uni.navigateTo({
						url: '/pages/resultPage/resultPage'
					})
				}

				this.$iBox.http('getEquipmentPolicy', {
					connectorId: this.detail.id
				})({
					method: 'get'
				}).then(res => {

					this.list = res.data

					let a = this.$moment().unix()
					let today = this.$moment().format('YYYY-MM-DD')
					
					let currentTime = ''
					this.list.forEach(item => {
						let startTime = today + ' ' + item.startTime
						let endTime = ''
						let s = this.$moment(startTime, 'YYYY-MM-DD HH:mm:ss').unix()
						if(item.startTime.includes('23')){
							endTime = this.$moment().add(1,'day').format('YYYY-MM-DD') +  ' ' + item.endTime
						}else{
							endTime = today + ' ' + item.endTime
						}
						
						let e = this.$moment(endTime, 'YYYY-MM-DD HH:mm:ss').unix()
						if (a < e && a > s) {
							currentTime = item
						}

					})

					this.currentTime = currentTime
					uni.hideLoading()
					console.log(currentTime);
				})
			})

			this.$iBox.http('getUserInfo', {

			})({
				method: 'get'
			}).then(res => {
				let centerUser = res.data
				centerUser.token = this.userInfo.token
				centerUser.session_key = this.userInfo.session_key
				this.centerUser = centerUser
				this.updateUserInfo(this.centerUser)
				this.$iBox.http('getUserCarWashCouponList', {
					pageNumber: 1,
					pageSize: 1000
				})({
					method: 'post'
				}).then(res => {

					this.couponList = res.data.list
				})

				this.bool = true
				this.$iBox.http('getUserCar', {

				})({
					method: 'get'
				}).then(res => {
					if (res.data.length > 0) {
						this.carNumber = res.data[0].carNumber
						this.carList = res.data
						this.current = res.data[0].id
						this.pushCar(this.carNumber)
					}
				})

			}).catch(function(error) {
				console.log('网络错误', error)
			})


		},
		methods: {
			...mapActions('login', ['pushCar', 'updateUserInfo', 'pushPNumer']),
			chooseCar(e) {
				this.carNumber = e.carNumber
				this.current = e.id
			},
			loginSucess() {

				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone) {
						this.if_login = false

					} else {
						this.if_login = true
					}
				})
			},
			choosePay(e) {
				this.payItem = e.id
			},
			filterList(e) {
				//0离线，1空闲，2占用（未充电），3占用（充电中），4占用（预约占用），255故障
				let list = [{
					id: 0,
					name: '离线'
				}, {
					id: 1,
					name: '空闲'
				}, {
					id: 2,
					name: '占用(未充电)'
				}, {
					id: 3,
					name: '占用(充电中)'
				}, {
					id: 4,
					name: '占用(预约占用)'
				}, {
					id: 255,
					name: '故障'
				}]

				let a = list.filter(item => {
					return item.id == e.status
				})[0]
				return a ? a.name : ''
			},
			toScan() {
				console.log('scan');
				uni.scanCode({
					onlyFromCamera: true,
					success: function(res) {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
					}
				});
			},
			addCar() {
				uni.navigateTo({
					url: '/pages/addCar/addCar'
				})
			},
			addPrice() {
				uni.navigateTo({
					url: '/pages/index/detail/priceList?id=' + this.detail.id
				})
			},

			choosePower(e) {
				this.current1 = e.id
				this.params.pageNumber = 1
				if (e.id == 2) {
					this.params.parkId = this.detail.id
					this.params.fastDevice = false
					this.$iBox.http('getEquipmentConnectorList', this.params)({
						method: 'post'
					}).then(res => {
						this.list = res.data.list
					})
				} else {
					this.params.parkId = this.detail.id
					this.params.fastDevice = true
					this.$iBox.http('getEquipmentConnectorList', this.params)({
						method: 'post'
					}).then(res => {
						this.list = res.data.list
					})
				}
			},
			startCharge() {
				this.$iBox.http('getConnectorInfo', {
					connectorId: this.pNumber
				})({
					method: 'get'
				}).then(res => {

					this.detail = res.data
					if (this.detail.chargingPileOrder) {
						uni.navigateTo({
							url: '/pages/resultPage/resultPage'
						})
					}

							this.if_login = false
							if (this.detail.status == 0) {
								uni.showToast({
									icon: 'error',
									title: '设备离线中...'
								})
								return
							} else if (this.detail.status == 1) {
								uni.showToast({
									icon: 'none',
									title: '请插入充电设备重试!'
								})
								return
							} else if (this.detail.status == 3) {
								uni.showToast({
									icon: 'error',
									title: '设备正在充电中!'
								})
								return
							} else if (this.detail.status == 4) {
								uni.showToast({
									icon: 'error',
									title: '设备已被预约!'
								})
								return
							} else if (this.detail.status == 255) {
								uni.showToast({
									icon: 'error',
									title: '设备故障!'
								})
								return
							}

							if (this.payItem == 1) {

								uni.navigateTo({
									url: '/pages/recharge/recharge?type=2'
								})
							} else {
								if (this.centerUser.userBalance && this.centerUser.userBalance.balance <
									5) {
									uni.showModal({
										title: '提示',
										content: '为了您的充电体验，请充值后重试!',
										success: (res) => {
											if (res.confirm) {
												uni.navigateTo({
													url: '/pages/recharge/recharge?type=1'
												})
											}
										}
									})
								} else {
									uni.showLoading({
										title: '正在启动...'
									})
									this.$iBox.http('startCharge', {
										payType: 1,
										carNumber: this.carNumber,
										connectorId: this.pNumber
									})({
										method: 'post'
									}).then(res => {
										uni.hideLoading()
										// 支付
										uni.navigateTo({
											url: '/pages/resultPage/resultPage'
										})

									}).catch(err => {
										console.log(err, 'err');
										uni.showModal({
											title: '提示',
											content: err.message + ',请插枪重试!',
											success: (res) => {
												// uni.navigateTo({
												// 	url: '/pages/recharge/recharge?type=1'
												// })
												uni.navigateBack()
											}
										})
									})
								}

							}

					this.$iBox.http('getEquipmentPolicy', {
						connectorId: this.detail.id
					})({
						method: 'get'
					}).then(res => {

						this.list = res.data

						let a = this.$moment().unix()
						let today = this.$moment().format('YYYY-MM-DD')
						let currentTime = ''
						this.list.forEach(item => {
							let startTime = today + ' ' + item.startTime
							let endTime = today + ' ' + item.endTime

							let s = this.$moment(startTime, 'YYYY-MM-DD HH:mm:ss').unix()
							let e = this.$moment(endTime, 'YYYY-MM-DD HH:mm:ss').unix()
							if (a < e && a > s) {
								currentTime = item
							}

						})

						this.currentTime = currentTime
						uni.hideLoading()
						console.log(currentTime);
					})
				})


			}
		},
		onUnload() {

		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">

</style>