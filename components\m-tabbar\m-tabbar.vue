<template>
	<view v-if="show" class="m-tabbar" @touchmove.stop.prevent="() => {}">
		<view class="m-tabbar_content" :style="index!=2?'width:'+100/list.length+'%':'30%'" v-for="(item, index) in list" :key="item.id" @click.stop="switchTabbar(index)">
			<view class="goat" v-if="index!=2">
				<image :src="setIconPath(index)" class="icon_style" style="width:44rpx; height: 44rpx;"></image>
				<text class="icon_text" :style="{color:setIconText(index)}">{{item.name}}</text>
			</view>
			<view class="" style="display: flex;background-color: #00C7A3;width: 230rpx;height: 90rpx;border-radius: 60rpx;display: flex;align-items: center;justify-content: center;margin: 0 20rpx;" v-else>
				<uv-icon name="scan" size="32" color="#FFFFFF"></uv-icon>
				<image :src="setIconPath(index)" style="width: 70rpx;height: 36rpx;"></image>
			</view>
		</view>
	</view>
	
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		props: {
			// 显示与否
			show: {
				type: Boolean,
				default: true
			},
			// 通过v-model绑定current值
			current: {
				type: [String, Number],
				default: 0
			},
			// 模式隐藏原生tabbar栏，自定义tabbar栏使用
			mode :{
				type:Boolean,
				default:true
			},
			list: {
				type:Array,
				default:[{
					icon_path: "/static/tabs/main.png",
					name: "K充电",
					path: "pages/index/index",
					select_icon_path: "/static/tabs/main_active.png"
				},
				{
					icon_path: "/static/tabs/wash.png",
					name: "K洗车",
					path: "pages/washCar/washCar",
					select_icon_path: "/static/tabs/wash_active.png"
				},
				{
					icon_path: "/static/tabs/scan.png",
					name: "扫一扫",
					path: "",
					big:true,
					select_icon_path: "/static/tabs/scan.png"
				},
				{
					icon_path: "/static/tabs/order.png",
					name: "订单",
					path: "pages/order/order",
					select_icon_path: "/static/tabs/order_active.png"
				},
				{
					icon_path: "/static/tabs/mine.png",
					name: "我的",
					path: "pages/myCenter/myCenter",
					select_icon_path: "/static/tabs/mine_active.png"
				}]
			}
			
		},
		data() {
			return {
				pageUrl: '', // 当前页面URL
			}
		},
		created() {
			// 隐藏原生tabbar
			console.log(this.list,'t');
			if(this.mode) uni.hideTabBar();
			// 获取当前地址的页面地址
			let pages = getCurrentPages();
			this.pageUrl = pages[pages.length - 1].route;
		},
		watch:{
			list(){
				console.log(this.list,'lll');
			}
		},
		computed: {
			// ...mapState('ui', ['themeColor']),
			setIconPath() {
				return (index) => {
					// console.log(index,'dsdsd');
					// 历遍m-tabbar的每一项item时，判断是否传入了pagePath参数，如果传入了
					// 和data中的pageUrl参数对比，如果相等，即可判断当前的item对应当前的tabbar页面，设置高亮图标
					// 采用这个方法，可以无需使用v-model绑定的value值
					let pagePath = this.list[index].path;
					// 如果定义了pagePath属性，意味着使用系统自带tabbar方案，否则使用一个页面用几个组件模拟tabbar页面的方案
					// 这两个方案对处理tabbar item的激活与否方式不一样
					if(pagePath) {
						console.log(pagePath,'pagePath');
						if(pagePath == this.pageUrl || pagePath == '/' + this.pageUrl) {
							return this.list[index].select_icon_path;
						} else {
							return this.list[index].icon_path;
						}
					} else {
						// 普通方案中，索引等于v-model值时，即为激活项
						return index == this.value ? this.list[index].select_icon_path : this.list[index].icon_path
					}
				}
			},
			setIconText() {
				return (index) => {
					console.log(index,this.pageUrl);
					// 判断方法同理于elIconPath
					let pagePath = this.list[index].path;
					if(pagePath) {
						if(pagePath == this.pageUrl || pagePath == '/' + this.pageUrl) return '#01322D';
						else return '#999999';
					} else {
						return index == this.value ? '#01322D' : '#999999';
					}
				}
			}
		},
		mounted() {
			
		},
		methods: {
			...mapActions('login', ['pushPNumer','pushWash']),
			// 切换tab
			switchTabbar(index) {
				// 发出事件和修改v-model绑定的值
				// console.log('change',this.list[index].path_url);
				// 如果有配置pagePath属性，使用uni.switchTab进行跳转
				
				if(this.list[index].path) {
					console.log(this.list[index].path,'index');
					uni.switchTab({
						url: '/'+ this.list[index].path
					})
				} else {
					console.log('scan');
					uni.scanCode({
						onlyFromCamera: true,
						success:  (res)=> {
							console.log('条码类型：' + res.scanType);
							console.log('条码内容：' + JSON.stringify(res))
							
							if(res.scanType == 'WX_CODE'){
								let query = decodeURIComponent(res.path.split('?')[1].split('=')[1])
								console.log(query,'qu');
								let id = this.$iBox.linkFormat(query, "i")
								let mode = this.$iBox.linkFormat(query, "m")
								let c = this.$iBox.linkFormat(query, "c")
								if(mode==1){
									this.$iBox.http('getCarWashShop', {id:id})({
										method: 'get'
									}).then(res => {  
										this.pushWash(res.data)
										uni.navigateTo({
											url:'/pages/washCar/detail/detail'
										})
									})
								}else{
									this.pushPNumer(number)
									uni.navigateTo({
										url:'/pages/index/detail/gunDetail'
									})
								}
								
							}else {
								
								if(res.result.includes('https')||res.result.includes('http')){
									let number = res.result.split('?')[1].split('=')[1]
									console.log(number,'number');
									this.pushPNumer(number)
									uni.navigateTo({
										url:'/pages/index/detail/gunDetail'
									})
								} else {
									
									let id = res.result.split('?')[1].split('&')[1].split('=')[1]
									if(id){
										this.$iBox.http('getCarWashShop', {id:id})({
											method: 'get'
										}).then(res => {  
											this.pushWash(res.data)
											uni.navigateTo({
												url:'/pages/washCar/detail/detail'
											})
										})
									}
								
									
								}
							}
							
							
						}
					});

					// 如果配置了papgePath属性，将不会双向绑定v-model传入的value值
					// 因为这个模式下，不再需要v-model绑定的value值了，而是通过getCurrentPages()适配
					this.$emit('input', index);
				}
			},
			// 计算角标的right值
			getOffsetRight(count, isDot) {
				// 点类型，count大于9(两位数)，分别设置不同的right值，避免位置太挤
				if(isDot) {
					return -20;
				} else if(count > 9) {
					return -40;
				} else {
					return -30;
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	

	.m-tabbar {
		display: flex;
		align-items: center;
		position: relative;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: 1000;
		min-height: 128rpx;
		background-color: #FFFFFF;
		// border-top: 1px solid #bfbfbf;
		padding-bottom:20rpx ;
		box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
		&_content {
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.goat{
				width: 120rpx;
				height: 128rpx;
				border-radius: 50%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				// background-color: #ffffff;
				margin-bottom: 0rpx;
				
				.icon_style{
					width: 54rpx;
					height: 54rpx;
				}
				
				.icon_text{
					font-size: 22rpx;
					margin-top: 8rpx;
				}
			}
			
			.no_goat {
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.icon_style{
					width: 54rpx;
					height: 54rpx;
				}
				
				.icon_text{
					font-size: 22rpx;
				}
			}
			
		}
	}
</style>
