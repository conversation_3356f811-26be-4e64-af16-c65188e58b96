<template>
	<view>
		<view class="box1">
			<view class="inner_box">
				<view class="avator_box">
					<button class=""
						style="width: 670rpx;height: 128rpx;background-color: rgba(0, 0, 0, 0);boder:none;position: relative;display: flex;align-items: center;justify-content: space-between;"
						id="shareBtn" open-type="chooseAvatar" type="primary" @chooseavatar="editArv">
						<view class="iconfont" style="display: flex;align-items: center;justify-content: center;">
							<image :src="avatar_url?avatar_url:'/static/tabs/mine_active.png'" style="width: 128rpx;height: 128rpx;border-radius: 50%;"
								mode=""></image>
		
						</view>
						<view class="" style="display: flex;align-items: center;">
							<text style="color: rgba(12, 15, 20, 0.92);font-size: 30rpx;padding-right: 30rpx;">上传头像</text>
							<view class="" style="">
								<uv-icon name="arrow-right" color="rgba(12, 15, 20, 0.52)" size="12"></uv-icon>
							</view>
						</view>
					</button>
		
				</view>
			</view>
		</view>

		<view class="InfoBox">
			<view class="nameBox">
				<text style="padding-right: 80rpx;">昵称</text>
				<view class="" style="width: 420rpx;display: flex;align-items: center;justify-content: flex-end;">
					<input type="nickname" class="weui-input" placeholder="请输入昵称" v-model="nickName" @change="getNickname" style="width: 240rpx;"/>
					<!-- <view class="" style="width: 80rpx;"> -->
					<view @click="sureEdit" style="border-radius: 8rpx;background-color: aquamarine;height: 60rpx;width: fit-content;padding: 10rpx;display: flex;align-items: center;justify-content: center;">
						确认修改
					</view>
					
					<!-- </view> -->
					
					
				</view>
			</view>
			<view class="nameBox" @click="toEdit({type:'phone',value:phone})">
				<text style="padding-right: 80rpx;">电话</text>
				<view class="" style="width: 400rpx;display: flex;align-items: center;justify-content: flex-end;">
					<text style="font-size: 30rpx;color: #999999;" v-if="!phone">请完善您的电话</text>
					<text style="font-size: 30rpx;" v-else>{{phone}}</text>
					<uv-icon name="arrow-right" color="rgba(12, 15, 20, 0.52)" size="12"></uv-icon>
				</view>
				
			</view>
			<view class="nameBox" @click="toFp">
				<text style="">发票管理</text>
				<view class="" style="width: 400rpx;display: flex;align-items: center;justify-content: flex-end;">
					<text style="font-size: 24rpx;color: #999999;">管理发票信息</text>
					<uv-icon name="arrow-right" color="rgba(12, 15, 20, 0.52)" size="12"></uv-icon>
				</view>
			</view>


		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				phone: '',
				nickName: '',
				avatar_url: '',
				idcard: '',
				date: '',
				sex: '保密',
				list: ['保密', '男', '女'],
				action: '',
				wxCode:'',
				centerUser: null,
			};
		},
		computed: {
			...mapState('login', ['userInfo', 'hotel'])
		},
		async onShow() {
			await this.$onLaunched;
			
			this.$iBox.http('getUserInfo', {
			
			})({
				method: 'get'
			}).then(res => {
				
				this.centerUser = res.data
				this.centerUser.token = this.userInfo.token
				this.centerUser.session_key = this.userInfo.session_key
				this.updateUserInfo(this.centerUser)
				console.log(this.centerUser,'this.centerUser');
				this.phone = this.centerUser.phone ? this.centerUser.phone : ''
				this.nickName = this.centerUser.nickname ? this.centerUser.nickname : ''
				this.avatar_url = this.centerUser.avatar_url ? this.centerUser.avatarUrl : ''
				
			}).catch(function(error) {
				console.log('网络错误', error)
			})
			
		
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			getNickname(e) {
				console.log(e);
				this.nickName = e.detail.value
			},
			toEdit(e){
				uni.navigateTo({
					url:'/pages/myCenter/editPage?type='+ JSON.stringify(e)
				})
			},
			toFp(){
				uni.navigateTo({
					url:'/pages/fapiao/fapiao'
				})
			},
			sureEdit(){
				
				if(!this.nickName){
					uni.showToast({
						icon:'none',
						title:'请输入昵称!'
					})
				}
				
				let params = {
					nickname:this.nickName
				}
				// 修改会员信息
				this.$iBox.http('updateUserInfo', params)({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title: '提示',
						content: '修改成功',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								// 更新用户信息
								// this.$iBox.http('getUserInfo', {
								// 	simple: false
								// })({
								// 	method: 'post'
								// }).then(res => {
								// 	let userInfo = res.data
								// 	userInfo.session_key = this.userInfo.session_key
								// 	this.updateUserInfo(userInfo)
								// })
							}
						}
					})
				})
			},
			editArv(e) {
				
				const {
					avatarUrl
				} = e.detail
				
				uni.getStorage({
					key: 'baseUrl',
					success: (res) => {
						console.log(res.data);
						this.action = res.data + '/resource/upload'
					}
				});
				
				uni.uploadFile({
					url: 'https://wx.kunfeizn.com/resource/upload', // 仅为示例，非真实的接口地址
					filePath: avatarUrl,
					header: {
						'Authorization': 'Bearer ' + this.userInfo.token
					},
					name: 'file',
					success: (uploadFileRes) => {
						console.log(uploadFileRes,'uploadFileRes');
						this.avatar_url = JSON.parse(uploadFileRes.data).data
						let params = {
							avatarUrl: this.avatar_url
						}
						
						// 修改会员信息
						this.$iBox.http('updateUserInfo', params)({
							method: 'post'
						}).then(res => {
							uni.showModal({
								title: '提示',
								content: '修改头像成功',
								showCancel: false,
								success: (res) => {
									if (res.confirm) {
										// 更新用户信息
										// this.$iBox.http('getUserInfo', {
										// 	simple: false
										// })({
										// 	method: 'post'
										// }).then(res => {
										// 	let userInfo = res.data
										// 	userInfo.session_key = this.userInfo.session_key
										// 	this.updateUserInfo(userInfo)
										// })
									}
								}
							})
						})
					}
				});

			},
			register() {
				

			}
		}
	}
</script>
<style>
	page {
		background-color: rgba(235, 239, 250, 1);
	}
</style>
<style lang="scss" scoped>
	button::after {
		border: none;
	}
	.box1 {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 10rpx;
		height: 120rpx;
		width: 90%;
		margin: 60rpx auto;
		// background-color: #eff3ff;
	
		.inner_box {
			display: flex;
			align-items: center;
		}
	}
	

	.InfoBox {
		padding:0 40rpx;

		.codeBtn {
			padding: 10rpx;
			border-radius: 6rpx;
			font-size: 22rpx;
		}

		.nameBox {
			padding: 0rpx 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 622rpx;
			height: 112rpx;
			border-radius: 32rpx;
			background-color: #FFFFFF;
			margin-top: 20rpx;
		}

		.check_contant {
			padding: 30rpx 0;
		}

		.btn_register {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 500rpx;
			height: 90rpx;
			border-radius: 20rpx;
			background: #5555ff;
			color: #FFFFFF;
		}
	}
</style>
