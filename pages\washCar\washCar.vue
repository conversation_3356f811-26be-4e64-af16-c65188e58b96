<template>
	<view>
		<view class="" @click="chooseShop" style="position: absolute;left: 24rpx;width: 460rpx;border-radius: 40rpx;
		background-color: #FFFFFF;padding: 0 10rpx;display: flex;align-items: center;" :style="{'top':searchBarTop+'px','height':searchBarHeight+'px'}">
			<view class="" style="display: flex;align-items: center;">
				<view
					style="padding-left: 10rpx;color: #222222;width: 260rpx;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;font-size: 26rpx;">
					点击搜索站点
				</view>
			</view>
			<text style="padding: 0 10rpx;color: #D9D9D9;">|</text>
			<view class="" style="color: #999999;display: flex;align-items: center;width: 180rpx;">
				<uv-icon name="search" color="#999999" size="22"></uv-icon>
				<text style="color: #999999;font-size: 24rpx;">搜索站点</text>
			</view>
		</view>
		<view class="" :style="{'height': statusBarHeight+40+'px'}">

		</view>
		<view class=" " style="padding: 24rpx;">
			<swiper class="swiper" style="height:290rpx;width: 702rpx;border-radius: 16rpx;" :autoplay="false">
				<swiper-item v-for="item in swiperList">
					<image :src="item" mode="" style="height:100%;width: 100%;border-radius: 16rpx; "></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 自助洗车入口模块 -->
		<view class="self-wash-section" @click="goToSelfWash">
			<view class="self-wash-card">
				<view class="card-left">
					<view class="wash-icon">
						<text class="icon-text">🚗</text>
					</view>
					<view class="wash-info">
						<text class="wash-title">自助洗车</text>
						<text class="wash-desc">24小时自助服务，方便快捷</text>
					</view>
				</view>
				<view class="card-right">
					<view class="enter-btn">
						<text class="btn-text">立即体验</text>
						<text class="arrow-icon">→</text>
					</view>
				</view>
			</view>
		</view>

		<view class=""
			style="height: 110rpx;padding: 0 24rpx;background-color: #FFFFFF;width: 100%;display: flex;align-items: center;">
			<text style="color: #222222;font-size: 36rpx;">附近站点</text>
		</view>
		<view class="shopList">
			<m-washCarList :list="shopListArr" v-if="hackReset"></m-washCarList>
		</view>
		<view class="" style="height: 140rpx;">

		</view>
		<m-tabbar></m-tabbar>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				swiperList: [],
				hackReset: false,
				bool: true,
				shopListArr: [],
				params1: {
					pageNumber: 1,
					pageSize: 10,
					longitude: '',
					latitude: '',
					shopName: null,
					telephone: null,
					address: null,
					remark: null
				},
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				statusBarHeight: 0,
				bool: true
			}
		},
		onReady() {
			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			this.statusBarHeight = systemInfo.statusBarHeight
			console.log(menuButtonInfo,this.statusBarHeight);
		},
		async onShow() {
			await this.$onLaunched;
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.bool = true
			this.$iBox.http('getMiniProgramSlideshowList', {
				moduleId: 1
			})({
				method: 'get'
			}).then(res => {
				let list = []
				res.data.forEach(item => {
					list.push(item.pic)
				})
				this.swiperList = list
				uni.hideLoading()
			})
			this.params1.pageNumber = 1
			this.getWashShopList()
		},
		methods: {
			...mapActions('login', ['toLogin', 'pushWashList', 'pushWash']),

			// 跳转到自助洗车页面
			goToSelfWash() {
				uni.navigateTo({
					url: '/pages/selfWash/selfWash'
				});
			},

			chooseShop() {
				console.log('w');
				uni.navigateTo({
					url: '/pages/searchPage/searchPage?type=wash'
				})
			},
			getWashShopList() {
				// 第一次调起授权， 成功后拉起微信收货地址页面
				uni.showLoading({
					title: '加载中...'
				})

				wx.getLocation({
					success: res => {
						this.params1.latitude = res.latitude
						this.params1.longitude = res.longitude
						this.params1.pageNumber = 1
						this.$iBox
							.http('getCarWashShopList', this.params1)({
								method: 'post'
							}).then(res => {
								this.shopListArr = res.data.list
								this.pushWashList(res.data.list);

							});

					},
					fail: err => {
						wx.getSetting({
							success: res2 => {
								if (!res2.authSetting['scope.userLocation']) {
									wx.authorize({
										scope: 'scope.userLocation',
										success: () => {
											// 第一次调起授权， 成功后拉起微信收货地址页面
											wx.getLocation({
												success: res => {
													this.params1.latitude =
														res.latitude
													this.params1.longitude =
														res.longitude
													this.params1
														.pageNumber = 1
													this.$iBox.http(
														'getCarWashShopList',
														this.params1)({
														method: 'post'
													}).then(
														res => {
															this.shopListArr =
																res
																.data
																.list
															this.pushWashList(
																res
																.data
																.list
																);
														});

												}
											});
										},
										fail: () => {
											wx.showModal({
												title: '提示',
												content: '为了更好的体验,请授权位置',
												cancelText: '不授权',
												cancelColor: '#999',
												confirmText: '授权',
												confirmColor: '#f94218',
												success:(res)=> {

													if (res.confirm) {
														console.log('lalala',
															res)
														uni.openSetting({
															success(
																res) {}
														});
													} else
													if (res.cancel) {
														this.params1.pageNumber = 1
														this.$iBox.http(
															'getCarWashShopList',
															this.params1)({
															method: 'post'
														}).then(
															res => {
																this.shopListArr =
																	res
																	.data
																	.list
																this.pushWashList(
																	res
																	.data
																	.list
																	);
															});

													}
												}
											});
										}
									});
								} else {
									//第一次调起授权， 成功后拉起微信收货地址页面

								}
							}
						});
					}
				});
			},
		},
		onReachBottom() {
			if (this.bool) {
				++this.params1.pageNumber
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getCarWashShopList', this.params1)({
					method: 'post'
				}).then(res => {
					let new_list = this.shopListArr.concat(res.data.list)
					this.shopListArr = new_list

					if (this.shopListArr.length == res.data.total) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		},
		onShareAppMessage() {

		}
	}
</script>

<style>
	page {
		background-color: #F4F6F8;
	}

	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">
// 自助洗车模块样式
.self-wash-section {
	padding: 24rpx;
	background: #F8F9FA;

	.self-wash-card {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 20rpx;
		padding: 32rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: -50%;
			right: -50%;
			width: 200%;
			height: 200%;
			background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
			animation: shimmer 3s ease-in-out infinite;
		}

		&:active {
			transform: scale(0.98);
		}

		.card-left {
			display: flex;
			align-items: center;
			flex: 1;

			.wash-icon {
				width: 80rpx;
				height: 80rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;

				.icon-text {
					font-size: 36rpx;
				}
			}

			.wash-info {
				display: flex;
				flex-direction: column;
				gap: 8rpx;

				.wash-title {
					font-size: 32rpx;
					font-weight: 700;
					color: #FFFFFF;
					text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
				}

				.wash-desc {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.9);
					text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
				}
			}
		}

		.card-right {
			.enter-btn {
				background: rgba(255, 255, 255, 0.2);
				border: 2rpx solid rgba(255, 255, 255, 0.3);
				border-radius: 40rpx;
				padding: 16rpx 24rpx;
				display: flex;
				align-items: center;
				gap: 8rpx;
				transition: all 0.2s ease;

				&:active {
					background: rgba(255, 255, 255, 0.3);
					transform: scale(0.95);
				}

				.btn-text {
					font-size: 26rpx;
					color: #FFFFFF;
					font-weight: 600;
				}

				.arrow-icon {
					font-size: 24rpx;
					color: #FFFFFF;
					font-weight: 600;
				}
			}
		}
	}
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	50% {
		transform: translateX(100%) translateY(100%) rotate(45deg);
	}
	100% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
}
</style>