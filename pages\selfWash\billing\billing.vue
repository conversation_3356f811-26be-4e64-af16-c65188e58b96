<template>
	<view class="billing-container">

		<!-- 洗车计费页面 -->
		<view class="wash-billing-page" :style="{ paddingTop: '10rpx', paddingBottom: !isActive ? '160rpx' : '32rpx' }">
			<!-- 设备信息卡片 -->
			<view class="device-card">
				<view class="device-header">
					<view class="device-icon">
						<uv-icon name="car" size="32" color="#4A90E2"></uv-icon>
					</view>
					<view class="device-info">
						<text class="device-name">{{deviceInfo.carWashShop ? deviceInfo.carWashShop.shopName : '自助洗车设备'}}</text>
						<text class="device-code">设备编号：{{deviceInfo.deviceCode}}</text>
						<text class="device-address" v-if="deviceInfo.carWashShop">{{deviceInfo.carWashShop.address}}</text>
					</view>
					<view class="device-status" :class="{'online': isDeviceOnline}">
						<text class="status-text">{{isDeviceOnline ? '启动中' : '未启动'}}</text>
					</view>
				</view>

				<!-- 店铺信息 -->
				<view class="shop-info" v-if="deviceInfo.carWashShop">
					<view class="shop-item">
						<uv-icon name="phone" size="16" color="#999"></uv-icon>
						<text class="shop-text">{{deviceInfo.carWashShop.telephone}}</text>
					</view>
					<view class="shop-item">
						<uv-icon name="clock" size="16" color="#999"></uv-icon>
						<text class="shop-text">{{deviceInfo.carWashShop.startTime}} - {{deviceInfo.carWashShop.endTime}}</text>
					</view>
				</view>
			</view>

			<!-- 当前订单信息 -->
			<view class="order-info-card" v-if="deviceInfo.carWashSelfOrder">
				<view class="order-header">
					<text class="order-title">当前订单</text>
					<text class="order-code">{{deviceInfo.carWashSelfOrder.orderCode}}</text>
				</view>
				<view class="order-details">
					<view class="order-item">
						<text class="item-label">订单金额</text>
						<text class="item-value">¥{{deviceInfo.carWashSelfOrder.amount || '0.00'}}</text>
					</view>
					<view class="order-item">
						<text class="item-label">开始时间</text>
						<text class="item-value">{{formatDateTime(deviceInfo.carWashSelfOrder.startTime)}}</text>
					</view>
					<view class="order-item" v-if="deviceInfo.carWashSelfOrder.endTime">
						<text class="item-label">结束时间</text>
						<text class="item-value">{{formatDateTime(deviceInfo.carWashSelfOrder.endTime)}}</text>
					</view>
				</view>

				<!-- 服务使用情况 -->
				<view class="service-usage" v-if="hasServiceUsage">
					<text class="usage-title">服务使用明细</text>
					<view class="usage-list">
						<view class="usage-item" v-if="deviceInfo.carWashSelfOrder.cx">
							<view class="usage-info">
								<text class="usage-name">🚿 冲洗</text>
								<text class="usage-time">{{formatDuration(deviceInfo.carWashSelfOrder.cx)}}</text>
							</view>
							<view class="usage-cost">
								<text class="cost-calculation">¥{{deviceInfo.carWashSelfOrder.cxPrice}}/秒</text>
								<text class="cost-total">¥{{(parseFloat(deviceInfo.carWashSelfOrder.cx || 0) * parseFloat(deviceInfo.carWashSelfOrder.cxPrice || 0)).toFixed(2)}}</text>
							</view>
						</view>
						<view class="usage-item" v-if="deviceInfo.carWashSelfOrder.pm">
							<view class="usage-info">
								<text class="usage-name">🧼 泡沫</text>
								<text class="usage-time">{{formatDuration(deviceInfo.carWashSelfOrder.pm)}}</text>
							</view>
							<view class="usage-cost">
								<text class="cost-calculation">¥{{deviceInfo.carWashSelfOrder.pmPrice}}/秒</text>
								<text class="cost-total">¥{{(parseFloat(deviceInfo.carWashSelfOrder.pm || 0) * parseFloat(deviceInfo.carWashSelfOrder.pmPrice || 0)).toFixed(2)}}</text>
							</view>
						</view>
						<view class="usage-item" v-if="deviceInfo.carWashSelfOrder.xc">
							<view class="usage-info">
								<text class="usage-name">🌪️ 吸尘</text>
								<text class="usage-time">{{formatDuration(deviceInfo.carWashSelfOrder.xc)}}</text>
							</view>
							<view class="usage-cost">
								<text class="cost-calculation">¥{{deviceInfo.carWashSelfOrder.xcPrice}}/秒</text>
								<text class="cost-total">¥{{(parseFloat(deviceInfo.carWashSelfOrder.xc || 0) * parseFloat(deviceInfo.carWashSelfOrder.xcPrice || 0)).toFixed(2)}}</text>
							</view>
						</view>
						<view class="usage-item" v-if="deviceInfo.carWashSelfOrder.xs">
							<view class="usage-info">
								<text class="usage-name">🧴 洗手</text>
								<text class="usage-time">{{formatDuration(deviceInfo.carWashSelfOrder.xs)}}</text>
							</view>
							<view class="usage-cost">
								<text class="cost-calculation">¥{{deviceInfo.carWashSelfOrder.xsPrice}}/秒</text>
								<text class="cost-total">¥{{(parseFloat(deviceInfo.carWashSelfOrder.xs || 0) * parseFloat(deviceInfo.carWashSelfOrder.xsPrice || 0)).toFixed(2)}}</text>
							</view>
						</view>
						<view class="usage-item" v-if="deviceInfo.carWashSelfOrder.zt">
							<view class="usage-info">
								<text class="usage-name">⏸️ 暂停</text>
								<text class="usage-time">{{formatDuration(deviceInfo.carWashSelfOrder.zt)}}</text>
							</view>
							<view class="usage-cost">
								<text class="cost-calculation">¥{{deviceInfo.carWashSelfOrder.ztPrice}}/秒</text>
								<text class="cost-total">¥{{(parseFloat(deviceInfo.carWashSelfOrder.zt || 0) * parseFloat(deviceInfo.carWashSelfOrder.ztPrice || 0)).toFixed(2)}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 实时计费状态 -->
			<view class="billing-status" :class="{'active': isActive, 'warning': isLowBalance}">
				<view class="status-header">
					<view class="status-icon">
						<uv-icon :name="isActive ? 'play' : 'pause'" 
								size="32" 
								:color="isActive ? '#52C41A' : '#FF4D4F'" 
								:class="{'rotating': isActive}"></uv-icon>
					</view>
					<view class="status-info">
						<text class="status-title">{{statusText}}</text>
						<text class="status-desc">{{statusDesc}}</text>
					</view>
				</view>
				
				<!-- 实时费用显示 -->
				<view class="cost-display">
					<view class="cost-item">
						<text class="cost-label">当前费用</text>
						<text class="cost-amount current">¥{{currentCost.toFixed(2)}}</text>
					</view>
					<view class="cost-item">
						<text class="cost-label">剩余余额</text>
						<text class="cost-amount balance" :class="{'warning': remainingBalance <= 5}">
							¥{{remainingBalance.toFixed(2)}}
						</text>
					</view>
				</view>
				
				<!-- 使用时长 -->
				<view class="time-display">
					<text class="time-label">使用时长</text>
					<text class="time-value">{{formatDuration(currentOrderUsageTime || usageTime)}}</text>
				</view>
			</view>

			<!-- 服务价格展示区域 -->
			<view class="service-section" v-if="priceList && priceList.length > 0">
				<view class="service-price-cards">
					<!-- 冲洗服务 -->
					<view class="price-card wash-card" v-if="getPriceByType('cx')">
						<view class="price-info">
							<text class="price-amount">¥{{getPriceByType('cx')}}</text>
							<text class="price-unit">/秒</text>
						</view>
						<view class="service-info">
							<view class="service-icon-small">
								<text class="service-emoji">🚿</text>
							</view>
							<text class="service-label">冲洗</text>
						</view>
					</view>

					<!-- 泡沫服务 -->
					<view class="price-card foam-card" v-if="getPriceByType('pm')">
						<view class="price-info">
							<text class="price-amount">¥{{getPriceByType('pm')}}</text>
							<text class="price-unit">/秒</text>
						</view>
						<view class="service-info">
							<view class="service-icon-small">
								<text class="service-emoji">🧼</text>
							</view>
							<text class="service-label">泡沫</text>
						</view>
					</view>

					<!-- 吸尘服务 -->
					<view class="price-card vacuum-card" v-if="getPriceByType('xc')">
						<view class="price-info">
							<text class="price-amount">¥{{getPriceByType('xc')}}</text>
							<text class="price-unit">/秒</text>
						</view>
						<view class="service-info">
							<view class="service-icon-small">
								<text class="service-emoji">🌪️</text>
							</view>
							<text class="service-label">吸尘</text>
						</view>
					</view>

					<!-- 洗手服务 -->
					<view class="price-card handwash-card" v-if="getPriceByType('xs')">
						<view class="price-info">
							<text class="price-amount">¥{{getPriceByType('xs')}}</text>
							<text class="price-unit">/秒</text>
						</view>
						<view class="service-info">
							<view class="service-icon-small">
								<text class="service-emoji">🧴</text>
							</view>
							<text class="service-label">洗手</text>
						</view>
					</view>

					<!-- 暂停服务 -->
					<view class="price-card pause-card" v-if="getPriceByType('zt')">
						<view class="price-info">
							<text class="price-amount">¥{{getPriceByType('zt')}}</text>
							<text class="price-unit">/秒</text>
						</view>
						<view class="service-info">
							<view class="service-icon-small">
								<text class="service-emoji">⏸️</text>
							</view>
							<text class="service-label">暂停</text>
						</view>
					</view>

					<!-- 最低消费 -->
					<view class="price-card minimum-card" v-if="getPriceByType('zd')">
						<view class="price-info">
							<text class="price-amount">¥{{getPriceByType('zd')}}</text>
							<text class="price-unit">/次</text>
						</view>
						<view class="service-info">
							<view class="service-icon-small">
								<text class="service-emoji">💰</text>
							</view>
							<text class="service-label">最低消费</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮区域 -->
			<view class="action-section">
				<!-- 余额不足警告 -->
				<view class="warning-section" v-if="isLowBalance">
					<view class="warning-content">
						<uv-icon name="warning" size="20" color="#FF4D4F"></uv-icon>
						<text class="warning-text">余额不足，请及时充值，否则设备将自动停止</text>
					</view>
					<uv-button size="small" type="primary" @click="goToCharge">
						立即充值
					</uv-button>
				</view>

				<!-- 使用说明 -->
				<view class="tips-section" v-if="!isActive">
					<view class="tip-title">
						<uv-icon name="info-circle" size="16" color="#1890FF"></uv-icon>
						<text class="tip-title-text">使用说明</text>
					</view>
					<view class="tips-list">
						<text class="tip-item">• 点击"开始使用"后，选择需要的服务</text>
						<text class="tip-item">• 系统将按使用时长实时计费扣费</text>
						<text class="tip-item">• 余额不足时设备会自动停止服务</text>
						<text class="tip-item">• 使用完毕请点击"结束使用"</text>
					</view>
				</view>

				<!-- 控制按钮组（仅在使用中显示） -->
				<view class="button-group" v-if="isActive">
					<view class="active-buttons">
						<uv-button
							type="warning"
							size="large"
							:loading="pauseLoading"
							@click="pauseBilling"
							class="control-button pause-button">
							<uv-icon name="pause" size="20" color="#fff" style="margin-right: 8rpx;"></uv-icon>
							暂停
						</uv-button>

						<uv-button
							type="error"
							size="large"
							:loading="stopLoading"
							@click="showStopConfirm"
							class="control-button stop-button">
							<uv-icon name="stop" size="20" color="#fff" style="margin-right: 8rpx;"></uv-icon>
							结束使用
						</uv-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 固定按钮 -->
		<view class="fixed-start-button">
			<!-- 设备未启动时显示开始使用按钮 -->
			<template v-if="!isDeviceOnline">
				<view class="button-status-info" v-if="!canStart">
					<text class="status-text">{{userBalance <= 0 ? '余额不足' : '无可用洗车卡'}}</text>
					<text class="status-hint">请先充值或购买洗车卡</text>
				</view>
				<uv-button
					type="primary"
					size="large"
					:loading="startLoading"
					:disabled="!canStart"
					@click="startBilling"
					class="main-button start-button">
					{{startLoading ? '启动中...' : '开始使用'}}
				</uv-button>
			</template>

			<!-- 设备启动状态时显示结束使用按钮 -->
			<template v-else>
				<uv-button
					type="error"
					size="large"
					:loading="stopLoading"
					@click="showStopConfirm"
					class="main-button stop-button">
					<uv-icon name="stop" size="20" color="#fff" style="margin-right: 8rpx;"></uv-icon>
					{{stopLoading ? '结束中...' : '结束使用'}}
				</uv-button>
			</template>
		</view>

		<!-- 支付弹窗 -->
		<uv-popup v-model="showPayment" mode="bottom" border-radius="16">
			<view class="payment-popup">
				<view class="popup-header">
					<text class="popup-title">确认支付</text>
					<uv-icon name="close" size="24" color="#999999" @click="showPayment = false"></uv-icon>
				</view>
				<view class="payment-detail">
					<view class="package-summary">
						<text class="package-name">{{selectedPackage.name}}</text>
						<text class="package-amount">¥{{selectedPackage.price}}</text>
					</view>
					<view class="payment-methods">
						<view class="method-item" 
							  v-for="method in paymentMethods" 
							  :key="method.type"
							  @click="selectPayMethod(method)"
							  :class="{'selected': selectedPayMethod === method.type}">
							<view class="method-info">
								<uv-icon :name="method.icon" size="32" :color="method.color"></uv-icon>
								<text class="method-name">{{method.name}}</text>
							</view>
							<uv-icon name="checkmark-circle" size="20" 
									:color="selectedPayMethod === method.type ? '#00C7A3' : '#CCCCCC'"></uv-icon>
						</view>
					</view>
				</view>
				<view class="payment-actions">
					<uv-button type="primary" size="large" @click="confirmPayment" :loading="paymentLoading">
						确认支付 ¥{{selectedPackage.price}}
					</uv-button>
				</view>
			</view>
		</uv-popup>

		<!-- 结束使用确认弹窗 -->
		<uv-modal 
			v-model="showConfirm"
			title="确认结束使用"
			:content="`本次使用费用：¥${currentCost.toFixed(2)}，确认结束吗？`"
			:show-cancel-button="true"
			confirm-text="确认结束"
			cancel-text="继续使用"
			@confirm="confirmStop"
			@cancel="showConfirm = false">
		</uv-modal>

		<!-- 余额不足强制停止弹窗 -->
		<uv-modal 
			v-model="showForceStop"
			title="余额不足"
			content="您的余额已不足，设备已自动停止，请充值后继续使用"
			:show-cancel-button="false"
			confirm-text="去充值"
			@confirm="goToCharge">
		</uv-modal>

		<!-- 服务切换确认弹窗 -->
		<uv-modal 
			v-model="showServiceConfirm"
			:title="serviceConfirmTitle"
			:content="serviceConfirmContent"
			:show-cancel-button="true"
			confirm-text="确认"
			cancel-text="取消"
			@confirm="confirmServiceChange"
			@cancel="cancelServiceChange">
		</uv-modal>
	</view>
</template>

<script>
export default {
	data() {
		return {
			pageType: 'billing', // 'recharge' 或 'billing'
			deviceId: '',
			orderId: '',
			deviceInfo: {},
			userBalance: 0,
			washCardCount: 0,
			userInfo: {}, // 用户信息
			payType: 1, // 1-账户余额 3-洗车卡
			priceList: [], // 价格列表
			priceInfo: {
				vacuumPrice: 0.6,   // 吸尘价格/分钟
				washPrice: 0.5,     // 洗车价格/分钟
				foamPrice: 0.8,     // 泡沫价格/分钟
				handwashPrice: 0.3  // 洗手价格/分钟
			},
			isActive: false,        // 是否正在使用
			activeServices: [],     // 当前激活的服务
			currentCost: 0,         // 当前费用
			usageTime: 0,           // 使用时长(秒)
			deviceTimer: null,      // 设备数据定时器
			billingTimer: null,     // 计费定时器
			startLoading: false,
			pauseLoading: false,
			stopLoading: false,
			showConfirm: false,
			showForceStop: false,
			showServiceConfirm: false,
			serviceConfirmTitle: '',
			serviceConfirmContent: '',
			pendingService: '',
			pendingAction: '',
			billingTimer: null,     // 计费定时器
			balanceCheckTimer: null, // 余额检查定时器
			
			// 充值相关
			showPayment: false,
			selectedPackage: {},
			selectedPayMethod: 'wechat',
			paymentLoading: false,
			paymentMethods: [
				{
					type: 'wechat',
					name: '微信支付',
					icon: 'wechat',
					color: '#07C160'
				},
				{
					type: 'alipay',
					name: '支付宝',
					icon: 'alipay',
					color: '#1677FF'
				}
			],
			
			navBarHeight: 0,
			statusBarHeight: 0,
			shopId:''
		}
	},
	
	computed: {
		// 设备是否在线
		isDeviceOnline() {
			// 使用selfCarMqtt.sbzt字段判断设备状态，0代表未启动，1代表已启动
			return this.deviceInfo.selfCarMqtt && (this.deviceInfo.selfCarMqtt.sbzt === 1 || this.deviceInfo.selfCarMqtt.sbzt === '1');
		},

		// 根据类型获取价格
		getPriceByType() {
			return (type) => {
				if (!this.priceList || this.priceList.length === 0) return '0.00';

				const priceItem = this.priceList.find(item => item.status === 1);
				if (!priceItem) return '0.00';

				switch (type) {
					case 'cx': return parseFloat(priceItem.cxPrice || 0).toFixed(2);
					case 'pm': return parseFloat(priceItem.pmPrice || 0).toFixed(2);
					case 'xc': return parseFloat(priceItem.xcPrice || 0).toFixed(2);
					case 'xs': return parseFloat(priceItem.xsPrice || 0).toFixed(2);
					case 'zt': return parseFloat(priceItem.ztPrice || 0).toFixed(2);
					case 'zd': return parseFloat(priceItem.zdPrice || 0).toFixed(2);
					default: return '0.00';
				}
			};
		},

		// 是否有服务使用记录
		hasServiceUsage() {
			const order = this.deviceInfo.carWashSelfOrder;
			if (!order) return false;
			return order.cx || order.pm || order.xc || order.xs || order.zt;
		},

		// 剩余余额
		remainingBalance() {
			return Math.max(0, this.userBalance - this.currentCost);
		},
		
		// 是否余额不足
		isLowBalance() {
			return this.remainingBalance <= 5; // 余额少于5元时警告
		},
		
		// 状态文本
		statusText() {
			if (!this.isDeviceOnline) return '设备待机中';
			if (!this.isActive) return '设备已启动';
			if (this.activeServices.length === 0) return '请选择服务';
			return '设备使用中';
		},

		// 状态描述
		statusDesc() {
			if (!this.isDeviceOnline) return '设备未启动，点击开始使用按钮启动设备';
			if (!this.isActive) return '设备已启动，等待开始使用';
			if (this.activeServices.length === 0) return '选择您需要的洗车服务';
			return `正在使用：${this.getActiveServiceNames()}`;
		},
		
		// 是否可以开始
		canStart() {
			return this.userBalance > 0 || this.washCardCount > 0;
		},

		// 当前订单的总使用时长（秒）
		currentOrderUsageTime() {
			const order = this.deviceInfo.carWashSelfOrder;
			if (!order) return 0;

			let totalSeconds = 0;
			totalSeconds += parseFloat(order.cx || 0);  // 冲洗时长
			totalSeconds += parseFloat(order.pm || 0);  // 泡沫时长
			totalSeconds += parseFloat(order.xc || 0);  // 吸尘时长
			totalSeconds += parseFloat(order.xs || 0);  // 洗手时长
			totalSeconds += parseFloat(order.zt || 0);  // 暂停时长

			return totalSeconds;
		}
	},
	
	onReady() {
		const systemInfo = wx.getSystemInfoSync();
		this.navBarHeight = systemInfo.statusBarHeight + 44;
		this.statusBarHeight = systemInfo.statusBarHeight;
	},
	
	async onLoad(options) {
		await this.$onLaunched;
		console.log(options, 'options');

		if (options && options.scene) {
			let query = decodeURIComponent(options.scene)
			this.deviceId = this.$iBox.linkFormat(query, "c")
			this.shopId = this.$iBox.linkFormat(query, "s")

			console.log('从二维码获取参数:', {
				deviceId: this.deviceId,
				shopId: this.shopId
			});
		} else if (options) {
			// 从页面参数获取
			this.deviceId = options.deviceId || options.deviceCode || '';
			this.shopId = options.shopId || '';

			console.log('从页面参数获取:', {
				deviceId: this.deviceId,
				shopId: this.shopId
			});
		}

		// 初始化页面
		await this.initPage();
	},
	
	onUnload() {
		this.clearAllTimers();
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 格式化日期时间
		formatDateTime(timeStr) {
			if (!timeStr) return '';
			try {
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			} catch (error) {
				console.log('时间格式化失败:', error);
				return timeStr;
			}
		},

		async initPage() {
			try {
				// 先加载设备信息，获取shopId
				await this.loadDeviceInfo();

				// 然后并行加载其他信息
				await Promise.all([
					this.loadUserBalance(),
					this.loadPriceInfo()
				]);

				// 注意：不在这里启动定时器，而是在loadDeviceInfo中根据设备状态决定
			} catch (error) {
				console.error('页面初始化失败:', error);
			}
		},
		
		// 选择充值套餐
		selectPackage(type, price) {
			let packageInfo = {};
			this.selectedPackage = packageInfo;
			this.showPayment = true;
		},
		
		// 选择支付方式
		selectPayMethod(method) {
			this.selectedPayMethod = method.type;
		},
		
		// 确认支付
		async confirmPayment() {
			this.paymentLoading = true;
			
			try {
				const res = await this.$iBox.http('createRechargeOrder', {
					packageType: this.selectedPackage.type,
					amount: this.selectedPackage.price,
					payMethod: this.selectedPayMethod
				})({ method: 'post' });
				
				if (res.code === 200) {
					// 调用支付
					await this.processPayment(res.data);
				} else {
					uni.showToast({
						title: res.msg || '创建订单失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('支付失败', error);
				uni.showToast({
					title: '支付失败，请重试',
					icon: 'none'
				});
			} finally {
				this.paymentLoading = false;
			}
		},
		
		// 处理支付
		async processPayment(orderData) {
			if (this.selectedPayMethod === 'wechat') {
				// 微信支付
				wx.requestPayment({
					...orderData.paymentParams,
					success: () => {
						this.paymentSuccess();
					},
					fail: (err) => {
						console.error('微信支付失败', err);
						uni.showToast({
							title: '支付失败',
							icon: 'none'
						});
					}
				});
			}
		},
		
		// 支付成功
		paymentSuccess() {
			this.showPayment = false;
			uni.showToast({
				title: '充值成功',
				icon: 'success'
			});
			
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		},
		
		// 拨打电话
		makeCall() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		
		// 显示帮助
		showHelp() {
			uni.showModal({
				title: '充值说明',
				content: '充值后可在该商家下面的所有门店消费，余额无有效期，用完为止。',
				showCancel: false
			});
		},

		// 计算实时费用
		calculateRealTimeCost() {
			if (!this.deviceInfo.carWashSelfOrder) {
				this.currentCost = 0;
				return;
			}

			const order = this.deviceInfo.carWashSelfOrder;
			let totalCost = 0;

			// 冲洗费用
			if (order.cx && order.cxPrice) {
				const seconds = parseFloat(order.cx);
				const price = parseFloat(order.cxPrice);
				totalCost += seconds * price;
				console.log(`冲洗费用: ${seconds}秒 × ¥${price} = ¥${seconds * price}`);
			}

			// 泡沫费用
			if (order.pm && order.pmPrice) {
				const seconds = parseFloat(order.pm);
				const price = parseFloat(order.pmPrice);
				totalCost += seconds * price;
				console.log(`泡沫费用: ${seconds}秒 × ¥${price} = ¥${seconds * price}`);
			}

			// 吸尘费用
			if (order.xc && order.xcPrice) {
				const seconds = parseFloat(order.xc);
				const price = parseFloat(order.xcPrice);
				totalCost += seconds * price;
				console.log(`吸尘费用: ${seconds}秒 × ¥${price} = ¥${seconds * price}`);
			}

			// 洗手费用
			if (order.xs && order.xsPrice) {
				const seconds = parseFloat(order.xs);
				const price = parseFloat(order.xsPrice);
				totalCost += seconds * price;
				console.log(`洗手费用: ${seconds}秒 × ¥${price} = ¥${seconds * price}`);
			}

			// 暂停费用
			if (order.zt && order.ztPrice) {
				const seconds = parseFloat(order.zt);
				const price = parseFloat(order.ztPrice);
				totalCost += seconds * price;
				console.log(`暂停费用: ${seconds}秒 × ¥${price} = ¥${seconds * price}`);
			}

			this.currentCost = totalCost;
			console.log(`总费用: ¥${totalCost.toFixed(2)}`);
		},

		// 启动设备数据定时器
		startDeviceTimer() {
			if (this.deviceTimer) {
				clearInterval(this.deviceTimer);
			}

			console.log('启动设备数据定时器');
			this.deviceTimer = setInterval(() => {
				// 只有设备启动状态才需要实时获取订单用量
				if (this.isDeviceOnline) {
					console.log('设备已启动，定时获取设备数据...');
					this.loadDeviceInfo();
				} else {
					console.log('设备未启动，跳过本次请求');
				}
			}, 5000); // 每5秒获取一次
		},

		// 停止设备数据定时器
		stopDeviceTimer() {
			if (this.deviceTimer) {
				console.log('停止设备数据定时器');
				clearInterval(this.deviceTimer);
				this.deviceTimer = null;
			}
		},

		// 加载设备信息
		async loadDeviceInfo() {
			try {
				const res = await this.$iBox.http('getSelfCarWashDevice', {
					deviceCode: this.deviceId
				})({ method: 'get' });

				console.log('设备信息响应:', res);

				if (res.code === 0 && res.data) {
					this.deviceInfo = res.data;

					// 设置shopId
					if (this.deviceInfo.carWashShop) {
						this.shopId = this.deviceInfo.carWashShop.id;
						console.log('设置shopId:', this.shopId);
					} else if (this.deviceInfo.shopId) {
						this.shopId = this.deviceInfo.shopId;
						console.log('设置shopId:', this.shopId);
					}

					// 如果有当前订单，处理订单信息
					if (this.deviceInfo.carWashSelfOrder) {
						const order = this.deviceInfo.carWashSelfOrder;
						console.log('当前订单信息:', order);

						// 根据订单状态设置页面状态
						if (order.status && order.endTime) {
							// 订单已结束
							this.isActive = false;
						} else if (order.startTime && !order.endTime) {
							// 订单进行中
							this.isActive = true;
							// 计算实时费用
							this.calculateRealTimeCost();
						}
					} else {
						// 没有订单
						this.isActive = false;
						this.currentCost = 0;
					}

					// 根据设备状态决定是否需要定时器
					if (this.isDeviceOnline) {
						// 设备已启动，需要实时获取数据
						if (!this.deviceTimer) {
							console.log('设备已启动，启动定时器');
							this.startDeviceTimer();
						}
					} else {
						// 设备未启动，停止定时器
						if (this.deviceTimer) {
							console.log('设备未启动，停止定时器');
							this.stopDeviceTimer();
						}
					}
				} else {
					console.error('设备信息接口返回异常:', res);
				}
			} catch (error) {
				console.error('加载设备信息失败', error);
				uni.showToast({
					title: '设备信息加载失败',
					icon: 'none'
				});
			}
		},
		
		// 加载用户余额
		async loadUserBalance() {
			try {
				const res = await this.$iBox.http('getCarWashSelfBalance', {})({
					method: 'get'
				});

				console.log('用户余额响应:', res);

				if (res.code === 0 && res.data) {
					// 根据新的接口数据结构处理
					this.userBalance = parseFloat(res.data.balance || 0);

					// 用户信息
					this.userInfo = {
						id: res.data.id,
						userId: res.data.userId,
						nickname: res.data.nickname,
						phone: res.data.phone,
						avatarUrl: res.data.avatarUrl,
						createTime: res.data.createTime
					};

					console.log('用户余额:', this.userBalance);
					console.log('用户信息:', this.userInfo);

					// 获取洗车卡数量（需要单独接口）
					await this.loadWashCardCount();
				} else {
					console.error('余额接口返回异常:', res);
					this.userBalance = 0;
				}
			} catch (error) {
				console.error('加载用户余额失败', error);
				this.userBalance = 0;
			}
		},

		// 加载洗车卡数量
		async loadWashCardCount() {
			try {
				const res = await this.$iBox.http('getUserOnceWashCardList', {
					pageNumber: 1,
					pageSize: 10
				})({
					method: 'post'
				});

				console.log('洗车卡响应:', res);

				if (res.code === 0 && res.data && res.data.list) {
					// 计算可用的洗车卡数量
					this.washCardCount = res.data.list.length;
					console.log('洗车卡数量:', this.washCardCount);
				} else {
					this.washCardCount = 0;
				}
			} catch (error) {
				console.error('加载洗车卡数量失败', error);
				this.washCardCount = 0;
			}
		},

		// 加载价格信息
		async loadPriceInfo() {
			console.log('开始加载价格信息, shopId:', this.shopId);
			try {
				// 优先从新接口获取价格列表
				await this.loadPriceList();

				// 如果新接口没有数据，尝试使用订单中的价格信息
				if ((!this.priceList || this.priceList.length === 0) && this.deviceInfo.carWashSelfOrder) {
					const order = this.deviceInfo.carWashSelfOrder;
					this.priceInfo = {
						vacuumPrice: parseFloat(order.xcPrice || 0.6),   // 吸尘价格
						washPrice: parseFloat(order.cxPrice || 0.5),     // 冲洗价格
						foamPrice: parseFloat(order.pmPrice || 0.8),     // 泡沫价格
						handwashPrice: parseFloat(order.xsPrice || 0.3), // 洗手价格
						pausePrice: parseFloat(order.ztPrice || 0),      // 暂停价格
						autoPrice: parseFloat(order.zdPrice || 0)        // 自动价格
					};
					return;
				}

			} catch (error) {
				console.error('加载价格信息失败', error);
				// 使用默认价格
				this.priceInfo = {
					vacuumPrice: 0.6,
					washPrice: 0.5,
					foamPrice: 0.8,
					handwashPrice: 0.3
				};
			}
		},

		// 加载价格列表
		async loadPriceList() {
			try {
				if (!this.shopId) {
					console.log('shopId为空，无法加载价格列表');
					return;
				}

				const res = await this.$iBox.http('getCarWashSelfPriceList', {
					shopId: this.shopId
				})({ method: 'get' });

				console.log('价格列表响应:', res);

				if (res.code === 0 && res.data) {
					this.priceList = Array.isArray(res.data) ? res.data : [res.data];
					console.log('价格列表加载成功:', this.priceList);
				} else {
					console.log('价格列表接口返回异常:', res);
					this.priceList = [];
				}
			} catch (error) {
				console.error('加载价格列表失败', error);
				this.priceList = [];
			}
		},
		
		// 开始计费
		async startBilling() {
			if (!this.canStart) {
				uni.showToast({
					title: '余额不足或无可用洗车卡',
					icon: 'none'
				});
				return;
			}
			
			this.startLoading = true;
			
			try {
				const res = await this.$iBox.http('startRealtimeBilling', {
					deviceId: this.deviceId,
					payType: this.payType
				})({ method: 'post' });
				
				if (res.code === 200) {
					this.isActive = true;
					this.orderId = res.data.orderId;
					this.startBillingTimer();
					
					uni.showToast({
						title: '设备已启动，请选择服务',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: res.msg || '启动失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('开始计费失败', error);
				uni.showToast({
					title: '启动失败，请重试',
					icon: 'none'
				});
			} finally {
				this.startLoading = false;
			}
		},
		
		// 启动计费定时器
		startBillingTimer() {
			this.usageTime = 0;
			this.currentCost = 0;
			
			// 每秒更新一次计费
			this.billingTimer = setInterval(() => {
				this.usageTime++;
				this.updateCost();
			}, 1000);
			
			// 每5秒检查一次余额
			this.balanceCheckTimer = setInterval(() => {
				this.checkBalance();
			}, 5000);
		},
		
		// 更新费用
		updateCost() {
			if (this.activeServices.length === 0) return;
			
			const minutes = this.usageTime / 60;
			let totalCost = 0;
			
			this.activeServices.forEach(service => {
				switch(service) {
					case 'wash':
						totalCost += minutes * this.priceInfo.washPrice;
						break;
					case 'foam':
						totalCost += minutes * this.priceInfo.foamPrice;
						break;
					case 'vacuum':
						totalCost += minutes * this.priceInfo.vacuumPrice;
						break;
					case 'handwash':
						totalCost += minutes * this.priceInfo.handwashPrice;
						break;
				}
			});
			
			this.currentCost = totalCost;
		},
		
		// 检查余额
		async checkBalance() {
			if (this.remainingBalance <= 0) {
				// 余额不足，强制停止
				await this.forceStop();
			} else if (this.remainingBalance <= 2) {
				// 余额即将不足，发送警告
				this.sendLowBalanceWarning();
			}
		},
		
		// 强制停止
		async forceStop() {
			this.clearAllTimers();
			this.isActive = false;
			this.activeServices = [];
			
			try {
				await this.$iBox.http('forceStopBilling', {
					orderId: this.orderId,
					reason: 'insufficient_balance'
				})({ method: 'post' });
			} catch (error) {
				console.error('强制停止失败', error);
			}
			
			this.showForceStop = true;
		},
		
		// 发送余额不足警告
		sendLowBalanceWarning() {
			uni.showToast({
				title: `余额不足¥${this.remainingBalance.toFixed(2)}，请及时充值`,
				icon: 'none',
				duration: 3000
			});
		},
		
		
		
		
		// 显示停止确认
		showStopConfirm() {
			this.showConfirm = true;
		},
		
		// 确认停止
		async confirmStop() {
			this.stopLoading = true;
			
			try {
				const res = await this.$iBox.http('stopRealtimeBilling', {
					orderId: this.orderId,
					totalCost: this.currentCost,
					usageTime: this.usageTime
				})({ method: 'post' });
				
				if (res.code === 200) {
					this.clearAllTimers();
					
					uni.showToast({
						title: '使用已结束',
						icon: 'success'
					});
					
					setTimeout(() => {
						uni.redirectTo({
							url: '/pages/selfWash/orders/orders'
						});
					}, 1500);
				} else {
					uni.showToast({
						title: res.msg || '结束失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('结束使用失败', error);
				uni.showToast({
					title: '结束失败，请重试',
					icon: 'none'
				});
			} finally {
				this.stopLoading = false;
				this.showConfirm = false;
			}
		},
		
		// 清除所有定时器
		clearAllTimers() {
			if (this.billingTimer) {
				clearInterval(this.billingTimer);
				this.billingTimer = null;
			}
			if (this.balanceCheckTimer) {
				clearInterval(this.balanceCheckTimer);
				this.balanceCheckTimer = null;
			}
			if (this.deviceTimer) {
				clearInterval(this.deviceTimer);
				this.deviceTimer = null;
			}
		},
		
		// 检查是否可以使用服务
		canUseService(service) {
			if (!this.isActive) return false;
			if (this.remainingBalance <= 0) return false;
			return true;
		},
		
		// 获取服务名称
		getServiceName(service) {
			const names = {
				vacuum: '吸尘',
				wash: '洗车',
				foam: '泡沫',
				handwash: '洗手'
			};
			return names[service] || service;
		},
		
		// 获取服务价格
		getServicePrice(service) {
			const prices = {
				vacuum: this.priceInfo.vacuumPrice,
				wash: this.priceInfo.washPrice,
				foam: this.priceInfo.foamPrice,
				handwash: this.priceInfo.handwashPrice
			};
			return prices[service] || 0;
		},
		
		// 获取激活服务名称
		getActiveServiceNames() {
			return this.activeServices.map(service => this.getServiceName(service)).join('、');
		},
		
		// 格式化时长（秒转分:秒）
		formatDuration(seconds) {
			const mins = Math.floor(seconds / 60);
			const secs = seconds % 60;
			return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
		},
		
		// 去充值
		goToCharge() {
			uni.navigateTo({
				url: '/pages/selfWash/billing/billing?type=recharge'
			});
		},
		
		// 返回
		goBack() {
			if (this.isActive) {
				uni.showModal({
					title: '提示',
					content: '设备正在使用中，确定要离开吗？离开后计费将继续进行。',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
			} else {
				uni.navigateBack();
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.billing-container {
	min-height: 100vh;
	background: #F4F6F8;
}
// 固定启动按钮样式
	.fixed-start-button {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #FFFFFF;
		padding: 8rpx 32rpx;
		padding-bottom: 8rpx;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;

		.button-status-info {
			text-align: center;
			margin-bottom: 16rpx;

			.status-text {
				display: block;
				font-size: 24rpx;
				color: #FF4D4F;
				font-weight: 500;
				margin-bottom: 4rpx;
			}

			.status-hint {
				font-size: 20rpx;
				color: #999999;
			}
		}

		.main-button {
			width: 100%;
			height: 88rpx;
			border-radius: 24rpx;
			font-size: 32rpx;
			font-weight: 600;
			background: linear-gradient(135deg, #1890FF 0%, #096DD9 100%);
			border: none;
			box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
			}

			&:disabled {
				background: #F5F5F5;
				color: #CCCCCC;
				box-shadow: none;
				transform: none;
			}
		}
	}

// 自定义导航栏样式
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #FFFFFF;
	z-index: 1000;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;

		.navbar-left {
			width: 80rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 40rpx;
			background: #F8F9FA;

			&:active {
				background: #E9ECEF;
			}
		}

		.navbar-center {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 4rpx;

			.navbar-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
			}

			.navbar-subtitle {
				font-size: 22rpx;
				color: #999999;
				max-width: 400rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}

		.navbar-right {
			width: 80rpx;
			display: flex;
			justify-content: flex-end;

			.device-status-indicator {
				display: flex;
				align-items: center;
				gap: 8rpx;
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				background: #F8F9FA;

				.status-dot {
					width: 12rpx;
					height: 12rpx;
					border-radius: 6rpx;
					background: #FF6B6B;
				}

				.status-text {
					font-size: 20rpx;
					color: #666666;
				}

				&.online {
					.status-dot {
						background: #52C41A;
					}

					.status-text {
						color: #52C41A;
					}
				}
			}
		}
	}
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #FFFFFF;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	
	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 32rpx;
	}
	
	.navbar-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
	
	.navbar-left, .navbar-right {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.help-icon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

/* 充值页面样式 */
.recharge-page {
	padding: 32rpx;
	
	.shop-info-header {
		background: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		
		.shop-name {
			font-size: 36rpx;
			font-weight: 600;
			color: #222222;
			margin-bottom: 20rpx;
			display: block;
		}
		
		.contact-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.contact-label {
				font-size: 28rpx;
				color: #666666;
			}
			
			.contact-phone {
				font-size: 28rpx;
				color: #333333;
				margin-left: 8rpx;
			}
			
			.call-btn {
				display: flex;
				align-items: center;
				padding: 8rpx 16rpx;
				background: #F0FFFE;
				border-radius: 20rpx;
				
				.call-text {
					font-size: 24rpx;
					color: #00C7A3;
					margin-left: 6rpx;
				}
			}
		}
	}
	
	.recharge-packages {
		.package-card {
			background: linear-gradient(135deg, #00E5CC 0%, #00C7A3 100%);
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 24rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 8rpx 24rpx rgba(0, 199, 163, 0.3);
			
			&.times-card {
				background: linear-gradient(135deg, #00E5CC 0%, #00C7A3 100%);
			}
			
			&.value-card {
				background: linear-gradient(135deg, #00E5CC 0%, #00C7A3 100%);
			}
			
			.card-left {
				flex: 1;
				
				.price-section {
					display: flex;
					align-items: baseline;
					margin-bottom: 16rpx;
					
					.currency {
						font-size: 28rpx;
						color: #FFFFFF;
						margin-right: 4rpx;
					}
					
					.price {
						font-size: 72rpx;
						font-weight: 700;
						color: #FFFFFF;
					}
				}
				
				.package-info {
					.package-title {
						display: block;
						font-size: 28rpx;
						color: #FFFFFF;
						margin-bottom: 8rpx;
						font-weight: 600;
					}
					
					.package-desc {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.8);
					}
				}
			}
			
			.card-right {
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.card-type {
					background: rgba(255, 255, 255, 0.2);
					color: #FFFFFF;
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 24rpx;
					margin-bottom: 16rpx;
				}
				
				.buy-btn {
					background: #FFFFFF;
					padding: 12rpx 24rpx;
					border-radius: 24rpx;
					
					.buy-text {
						font-size: 26rpx;
						color: #00C7A3;
						font-weight: 600;
					}
				}
			}
		}
	}
	
	.recharge-notes {
		background: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-top: 30rpx;
		
		.notes-title {
			font-size: 28rpx;
			font-weight: 600;
			color: #333333;
			margin-bottom: 20rpx;
		}
		
		.notes-list {
			.note-item {
				display: block;
				font-size: 24rpx;
				color: #666666;
				line-height: 1.6;
				margin-bottom: 12rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
}

/* 洗车计费页面样式 */
.wash-billing-page {
	.device-card {
		margin: 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.device-header {
			display: flex;
			align-items: center;
		}
		
		.device-icon {
			width: 80rpx;
			height: 80rpx;
			background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 24rpx;
		}
		
		.device-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 8rpx;

			.device-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.2;
			}

			.device-code {
				font-size: 24rpx;
				color: #999;
			}

			.device-address {
				font-size: 22rpx;
				color: #666;
				line-height: 1.3;
				max-width: 400rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		
		.device-status {
			padding: 8rpx 16rpx;
			border-radius: 12rpx;
			background: #F5F5F5;
			
			&.online {
				background: #F6FFED;
				
				.status-text {
					color: #52C41A;
				}
			}
			
			.status-text {
				font-size: 24rpx;
				color: #999;
			}
		}

		.shop-info {
			margin-top: 24rpx;
			padding-top: 24rpx;
			border-top: 1rpx solid #F0F0F0;
			display: flex;
			justify-content: space-between;

			.shop-item {
				display: flex;
				align-items: center;
				gap: 8rpx;

				.shop-text {
					font-size: 22rpx;
					color: #666;
				}
			}
		}
	}

	// 用户信息卡片样式
	.user-info-card {
		margin: 0 32rpx 32rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.2);

		.user-header {
			display: flex;
			align-items: center;
			gap: 24rpx;

			.user-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 40rpx;
				overflow: hidden;
				background: rgba(255, 255, 255, 0.2);

				.avatar-img {
					width: 100%;
					height: 100%;
					border-radius: 40rpx;
				}

				.avatar-placeholder {
					width: 100%;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgba(255, 255, 255, 0.3);

					.avatar-text {
						font-size: 32rpx;
						font-weight: 600;
						color: #FFFFFF;
					}
				}
			}

			.user-details {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 8rpx;

				.user-name {
					font-size: 32rpx;
					font-weight: 600;
					color: #FFFFFF;
					text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
				}

				.user-phone {
					font-size: 24rpx;
					color: rgba(255, 255, 255, 0.9);
				}

				.user-id {
					font-size: 22rpx;
					color: rgba(255, 255, 255, 0.7);
				}
			}

			.user-balance-display {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				gap: 8rpx;

				.balance-label {
					font-size: 22rpx;
					color: rgba(255, 255, 255, 0.8);
				}

				.balance-amount {
					font-size: 36rpx;
					font-weight: 700;
					color: #FFFFFF;
					text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
				}
			}
		}
	}

	// 订单信息卡片样式
	.order-info-card {
		margin: 0 32rpx 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border-left: 4rpx solid #1890FF;

		.order-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;

			.order-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
			}

			.order-code {
				font-size: 24rpx;
				color: #1890FF;
				background: rgba(24, 144, 255, 0.1);
				padding: 8rpx 16rpx;
				border-radius: 12rpx;
			}
		}

		.order-details {
			margin-bottom: 24rpx;

			.order-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 16rpx 0;
				border-bottom: 1rpx solid #F0F0F0;

				&:last-child {
					border-bottom: none;
				}

				.item-label {
					font-size: 28rpx;
					color: #666666;
				}

				.item-value {
					font-size: 28rpx;
					color: #333333;
					font-weight: 500;
				}
			}
		}

		.service-usage {
			.usage-title {
				font-size: 28rpx;
				font-weight: 600;
				color: #333333;
				margin-bottom: 16rpx;
			}

			.usage-list {
				display: flex;
				flex-direction: column;
				gap: 16rpx;

				.usage-item {
					background: #F8F9FA;
					border-radius: 12rpx;
					padding: 16rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.usage-info {
						display: flex;
						flex-direction: column;
						gap: 4rpx;

						.usage-name {
							font-size: 26rpx;
							color: #333333;
							font-weight: 500;
						}

						.usage-time {
							font-size: 22rpx;
							color: #666666;
						}
					}

					.usage-cost {
						display: flex;
						flex-direction: column;
						align-items: flex-end;
						gap: 4rpx;

						.cost-calculation {
							font-size: 20rpx;
							color: #999999;
						}

						.cost-total {
							font-size: 24rpx;
							color: #1890FF;
							font-weight: 600;
						}
					}
				}
			}
		}
	}

	
	.billing-status {
		margin: 0 32rpx 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 2rpx solid #F0F0F0;
		transition: all 0.3s ease;
		
		&.active {
			border-color: #52C41A;
			background: linear-gradient(135deg, #F6FFED 0%, #FFFFFF 100%);
		}
		
		&.warning {
			border-color: #FF4D4F;
			background: linear-gradient(135deg, #FFF2F0 0%, #FFFFFF 100%);
		}
		
		.status-header {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;
			
			.status-icon {
				margin-right: 16rpx;
			}
			
			.status-info {
				flex: 1;
				
				.status-title {
					display: block;
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.status-desc {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
		
		.cost-display {
			display: flex;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.cost-item {
				text-align: center;
				flex: 1;

				.cost-label {
					display: block;
					font-size: 24rpx;
					color: #999;
					margin-bottom: 8rpx;
				}

				.cost-amount {
					font-size: 36rpx;
					font-weight: 700;
					transition: all 0.3s ease;

					&.current {
						color: #1890FF;
						animation: costUpdate 0.5s ease;
					}

					&.balance {
						color: #52C41A;

						&.warning {
							color: #FF4D4F;
							animation: pulse 1s infinite;
						}
					}
				}
			}
		}
		
		.time-display {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 16rpx;
			background: #FAFAFA;
			border-radius: 12rpx;
			
			.time-label {
				font-size: 24rpx;
				color: #666;
				margin-right: 16rpx;
			}
			
			.time-value {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}
		}
	}
	
	.service-section {
		margin: 0 32rpx 32rpx;

		.service-price-cards {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 16rpx;

			// 小屏幕适配
			@media (max-width: 600rpx) {
				grid-template-columns: repeat(2, 1fr);
				gap: 12rpx;
			}

			.price-card {
				background: #FFFFFF;
				border-radius: 16rpx;
				padding: 20rpx 12rpx;
				text-align: center;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
				min-height: 110rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				transition: transform 0.2s ease;

				&:active {
					transform: scale(0.98);
				}

				&.wash-card {
					background: linear-gradient(135deg, #E3F2FD 0%, #FFFFFF 100%);
					border-left: 4rpx solid #1890FF;
				}

				&.foam-card {
					background: linear-gradient(135deg, #F0FFFE 0%, #FFFFFF 100%);
					border-left: 4rpx solid #00C7A3;
				}

				&.handwash-card {
					background: linear-gradient(135deg, #FFF7E6 0%, #FFFFFF 100%);
					border-left: 4rpx solid #FF7E32;
				}

				&.vacuum-card {
					background: linear-gradient(135deg, #F9F0FF 0%, #FFFFFF 100%);
					border-left: 4rpx solid #722ED1;
				}

				&.pause-card {
					background: linear-gradient(135deg, #FFF1F0 0%, #FFFFFF 100%);
					border-left: 4rpx solid #FF4D4F;
				}

				&.minimum-card {
					background: linear-gradient(135deg, #FFFBE6 0%, #FFFFFF 100%);
					border-left: 4rpx solid #FAAD14;
				}

				.price-info {
					display: flex;
					align-items: baseline;
					justify-content: center;
					margin-bottom: 10rpx;

					.price-amount {
						font-size: 26rpx;
						font-weight: 700;
						color: #333333;
					}

					.price-unit {
						font-size: 16rpx;
						color: #666666;
						margin-left: 4rpx;
					}
				}

				.service-info {
					display: flex;
					align-items: center;
					justify-content: center;

					.service-icon-small {
						width: 32rpx;
						height: 32rpx;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 8rpx;
						background: rgba(255, 255, 255, 0.2);

						.service-emoji {
							font-size: 20rpx;
							line-height: 1;
						}
					}

					.service-label {
						font-size: 20rpx;
						color: #666666;
						font-weight: 500;
					}
				}
			}
		}
	}
	
	.payment-info {
		margin: 0 32rpx 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 24rpx 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.payment-method {
			display: flex;
			align-items: center;
			
			.method-name {
				font-size: 28rpx;
				font-weight: 600;
				color: #333;
				margin: 0 16rpx;
			}
			
			.method-balance {
				font-size: 24rpx;
				color: #666;
				margin-left: auto;
			}
		}
	}
	
	.action-section {
		margin: 0 32rpx 32rpx;
		
		.warning-section {
			background: linear-gradient(135deg, #FFF2F0 0%, #FFFFFF 100%);
			border: 1rpx solid #FFCCC7;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.warning-content {
				display: flex;
				align-items: center;
				flex: 1;
				margin-right: 16rpx;
				
				.warning-text {
					font-size: 24rpx;
					color: #FF4D4F;
					margin-left: 8rpx;
				}
			}
		}
		
		.tips-section {
			background: #F8FAFE;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-bottom: 24rpx;
			
			.tip-title {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;
				
				.tip-title-text {
					font-size: 28rpx;
					font-weight: 600;
					color: #1890FF;
					margin-left: 8rpx;
				}
			}
			
			.tips-list {
				.tip-item {
					display: block;
					font-size: 24rpx;
					color: #666;
					line-height: 1.6;
					margin-bottom: 8rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
		}
		
		.button-group {
			.main-button {
				width: 100%;
				height: 88rpx;
				border-radius: 24rpx;
				font-size: 32rpx;
				font-weight: 600;
				
				&.start-button {
					background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
					box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
				}
			}
			
			.active-buttons {
				display: flex;
				gap: 16rpx;
				
				.control-button {
					flex: 1;
					height: 88rpx;
					border-radius: 24rpx;
					font-size: 28rpx;
					font-weight: 600;
					
					&.pause-button {
						background: linear-gradient(135deg, #FAAD14 0%, #FFC53D 100%);
						box-shadow: 0 8rpx 24rpx rgba(250, 173, 20, 0.3);
					}
					
					&.stop-button {
						background: linear-gradient(135deg, #FF4D4F 0%, #FF7875 100%);
						box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);
					}
				}
			}
		}
	}
}

/* 支付弹窗样式 */
.payment-popup {
	padding: 40rpx 30rpx;
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #222222;
		}
	}
	
	.payment-detail {
		.package-summary {
			background: #F8F9FA;
			border-radius: 12rpx;
			padding: 24rpx;
			margin-bottom: 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.package-name {
				font-size: 28rpx;
				color: #333333;
			}
			
			.package-amount {
				font-size: 32rpx;
				font-weight: 600;
				color: #FF7E32;
			}
		}
		
		.payment-methods {
			.method-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx;
				border: 2rpx solid #F0F0F0;
				border-radius: 12rpx;
				margin-bottom: 16rpx;
				
				&.selected {
					border-color: #00C7A3;
					background: #F0FFFE;
				}
				
				.method-info {
					display: flex;
					align-items: center;
					
					.method-name {
						font-size: 28rpx;
						color: #333333;
						margin-left: 16rpx;
					}
				}
			}
		}
	}
	
	.payment-actions {
		margin-top: 40rpx;
	}
}

/* Animation Effects */
@keyframes rotating {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.rotating {
	animation: rotating 2s linear infinite;
}

/* Responsive Design */
@media screen and (max-width: 750rpx) {
	.recharge-page, .wash-billing-page {
		padding: 24rpx;
	}
	
	.service-grid {
		grid-template-columns: 1fr !important;
		gap: 12rpx !important;
	}
	
	.active-buttons {
		flex-direction: column !important;
		gap: 12rpx !important;

		.control-button {
			flex: none !important;
		}
	}
}

// 动画关键帧
@keyframes costUpdate {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
		color: #FF7E32;
	}
	100% {
		transform: scale(1);
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
	100% {
		opacity: 1;
	}
}
</style>
