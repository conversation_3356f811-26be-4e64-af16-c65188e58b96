<template>
	<view>
		<uv-sticky bgColor="#fff">
			<uv-tabs :list="statusList" :current="current" @click="click" :scrollable='false' lineColor="#077E6C"
				lineWidth="40"></uv-tabs>
			<view class="" style="margin-top: 30rpx;height: 100rpx;display: flex;align-items: center;padding: 0 36rpx;">
				<view class="" @click="chooseType(item)" v-for="item in typeList"
					:style="typeId==item.id?'background:#C8FFEF;color:#077E6C;':'color:#999999;'"
					style="font-size: 24rpx;width: fit-content;padding: 10rpx;background-color: #EFEFEF;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;margin-right: 20rpx;">
					{{item.name}}
				</view>
			</view>
		</uv-sticky>
		<view class="" v-for="item in chargeOrderList" v-if="current==0&&typeId==2&&chargeOrderList.length>0"
			@click="toChoosePower(item)"
			style="margin: 20rpx auto; min-height: 580rpx;background-color: #FFFFFF;position: relative;display: flex;align-items: center;justify-content: space-between;padding: 0 20rpx;">
			<view class=""
				style="width: 48rpx;height: 48rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;"
				:style="washIds.includes(item.id)?'background-color: #e2e2e2;':'background-color: #e2e2e2;'">
				<uv-icon name="checkbox-mark" v-if="chargeIds.includes(item.id)" color=" #FF7E32"></uv-icon>
			</view>
			<view class="" style="width: 640rpx;">
				<view class="" style="height: 72rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;
				padding: 0 30rpx;background-color: #FFF6ED;font-size: 24rpx;color: #FF7E32;">
					<view class="">
						<text>充电订单</text>
						<text style="margin-left: 8rpx;">{{item.orderNo}}</text>
					</view>
					<text>{{item.chargeStatus==4?'已完成':'充电中'}}</text>
				</view>
				<view class="" style="min-height: 498rpx;width: 100%;padding: 30rpx;">
					<view class="" style="font-size: 32rpx;font-weight: 600;color: #222222;">
						{{item.stationName}}
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">设备名称:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.deviceName?item.deviceName:'暂无'}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">开始时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.startTime}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">结束时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.endTime}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">充电模式:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.chargeModel==1?'慢充':'快充'}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">车牌号码:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.plateNo}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">充电时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.timeLen}}分钟</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">充电度数:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.power?item.power:'暂无'}}度</text>
					</view>
					<view class="" style="width: 640rpx;height: 1px;background-color: #EFEFEF;margin: 10rpx auto;">

					</view>
					<view class="" style="display: flex;align-items: center;margin-top: 14rpx;">
						<view class="" style="display: flex;align-items: center;">
							<text style="font-size: 26rpx;color: #222222;">总计费用</text>
							<text style="font-size: 26rpx;color: #222222;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.totalMoney}}</text>
						</view>
					</view>
					<view class="" style="display:flex;align-items: center;">
						<view class="" style="display: flex;align-items: center;">
							<text style="font-size: 20rpx;color: #333333;">服务费</text>
							<text style="font-size: 26rpx;color: #333333;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.seviceMoney}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
							<text style="font-size: 20rpx;color: #333333;">优惠</text>
							<text style="font-size: 26rpx;color: #333333;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.discounts?item.discounts:0}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
							<text style="font-size: 20rpx;color: #333333;">电费</text>
							<text style="font-size: 26rpx;color: #333333;">￥</text>
							<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.elecMoney}}</text>
						</view>
					</view>
					<view class=""
						style="font-size: 22rpx;color: #999999;height: 50rpx;margin-top: 10rpx;height: 50rpx;display: flex;align-items: flex-end;"
						@click.stop="toPowerQuestion">
						<text style="text-align: center;">订单反馈</text>
						<uv-icon name="arrow-right" size="10" color="#999999"></uv-icon>
					</view>
				</view>
			</view>

		</view>
		<view class="" v-for="item in washOrderList" v-if="current==0&&typeId==0&&washOrderList.length>0"
			@click="tochooseWash(item)"
			style="margin: 20rpx auto; min-height: 450rpx;width: 702rpx;background-color: #FFFFFF;position: relative;display: flex;align-items: center;justify-content: space-between;">
			<view class=""
				style="width: 48rpx;height: 48rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;"
				:style="washIds.includes(item.id)?'background-color: #e2e2e2;':'background-color: #e2e2e2;'">
				<uv-icon name="checkbox-mark" v-if="washIds.includes(item.id)" color=" #FF7E32"></uv-icon>
			</view>
			<view class="" style="width: 640rpx;">
				<view class="" style="height: 72rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;
				padding: 0 30rpx;background-color: #FFF6ED;font-size: 24rpx;color: #FF7E32;">
					<view class="">
						<text>洗车订单</text>
						<text style="margin-left: 8rpx;">{{item.orderCode}}</text>
					</view>
					<text>{{filterOrder(item.status)}}</text>
				</view>
				<view class="" style="min-height: 378rpx;width: 100%;padding: 30rpx;">
					<view class="" style="font-size: 32rpx;font-weight: 600;color: #222222;">
						{{item.shopName}}
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">支付时间:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.createTime}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">支付类型:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{filterPay(item.payType)}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">洗车模式:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.washModeName}}</text>
					</view>
					<view class="" style="font-size: 22rpx;display: flex;align-items: center;margin-top: 20rpx;">
						<text style="color: #CCCCCC;">车牌号码:</text>
						<text style="color: #222222;margin-left: 20rpx;">{{item.carNumber}}</text>
					</view>
					<view class="" style="width: 640rpx;height: 1px;background-color: #EFEFEF;margin: 10rpx auto;">

					</view>
					<view class=""
						style="display: flex;align-items: center;margin-top: 14rpx;justify-content: space-between;">
						<view class="" style="display: flex;align-items: center;">
							<view class="" style="display: flex;align-items: center;">
								<text style="font-size: 20rpx;color: #222222;">洗车费用</text>
								<text style="font-size: 26rpx;color: #222222;">￥</text>
								<text style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.price}}</text>
							</view>
							<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
								<text style="font-size: 20rpx;color: #222222;">卡券减免</text>
								<text style="font-size: 26rpx;color: #222222;">￥</text>
								<text
									style="font-size: 34rpx;color: #FF7E32;font-weight: 600;">{{item.discounts}}</text>
							</view>
						</view>

						<view class="" style="display: flex;align-items: center;margin-left: 40rpx;">
							<text style="font-size: 20rpx;color: #222222;">实付</text>
							<text style="font-size: 26rpx;color: #222222;">￥</text>
							<text style="font-size: 40rpx;color: #FF7E32;font-weight: 600;">{{item.amount}}</text>
						</view>
					</view>
					<view class=""
						style="font-size: 22rpx;color: #999999;height: 50rpx;display: flex;align-items: flex-end;"
						@click.stop="toWashQuestion(item.id)">
						<text style="text-align: center;">订单反馈</text>
						<uv-icon name="arrow-right" size="10" color="#999999"></uv-icon>
					</view>

				</view>
			</view>

		</view>
		<view class="" v-if="invoiceList.length>0&&current!=0"
			style="min-height: 440rpx;width: 700rpx;border-radius: 36rpx;margin: 20rpx auto;background-color: #FFFFFF;"
			v-for="item in invoiceList">
			<view class=""
				style="height: 72rpx;width: 100%;background-color: #FFF6ED;display: flex;align-items: center;padding: 0 30rpx;justify-content: space-between;">
				<view class="" style="display: flex;font-size: 26rpx;color: #FF7E32;">
					订单ID：{{item.orderId}}
				</view>
				<view class="" style="font-size: 26rpx;color: #FF7E32;">
					{{item.status==1?'申请中':(item.status==2?'已完成':'无效')}}
				</view>
			</view>
			<view class="" style="width: 100%;padding: 30rpx;">
				<view class="" style="display: flex;align-items: center;">
					<text style="font-size: 26rpx;color: #CCCCCC;">发票类型</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.applyInvoiceType==1?'企业':'个人'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
					<text style="font-size: 26rpx;color: #CCCCCC;">抬头名称</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.invoiceName?item.invoiceName:'暂无'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;"
					v-if="item.applyInvoiceType==1">
					<text style="font-size: 26rpx;color: #CCCCCC;">企业税号</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.companyNumber?item.companyNumber:'暂无'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
					<text style="font-size: 26rpx;color: #CCCCCC;">电话</text>
					<text style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.tel}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
					<text style="font-size: 26rpx;color: #CCCCCC;">邮箱地址</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.email?item.email:'暂无'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;"
					v-if="item.applyInvoiceType==1">
					<text style="font-size: 26rpx;color: #CCCCCC;">开户银行</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.bankName?item.bankName:'暂无'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;"
					v-if="item.applyInvoiceType==1">
					<text style="font-size: 26rpx;color: #CCCCCC;">开户银行账号</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.bankNumber?item.bankNumber:'暂无'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;"
					v-if="item.applyInvoiceType==1">
					<text style="font-size: 26rpx;color: #CCCCCC;">企业地址</text>
					<text
						style="color: #222222;font-size: 26rpx;margin-left: 20rpx;">{{item.address?item.address:'暂无'}}</text>
				</view>

			</view>
			<view class=""
				style="display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;width: 100%;">
				<view class="" style="height: 80rpx;display: flex;align-items: center;">
					<text>发票金额:</text>
					<text style="color: #222222;font-size: 36rpx;margin-left: 20rpx;">{{item.money}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;" @click="downLoadFile(item.fileUrl)"
					v-if="current==2">
					<uv-icon name="file-text-fill" color="#2979ff" size="20"></uv-icon>
					<p style="font-size: 24rpx;text-overflow: ellipsis;overflow: hidden;
															  white-space: nowrap;">下载发票</p>
				</view>
			</view>

		</view>
		<view class="" style="margin-top: 80rpx;"
			v-if="(invoiceList.length==0&&current!=0)||(current==0&&((washOrderList.length==0&&typeId==0)||(chargeOrderList.length==0&&typeId==3)))">
			<uv-empty mode="list" icon="https://www.kemanfang.net/xcx_resource/fpl.png" text="暂无发票"
				style="height: 420rpx;width: 420rpx;"></uv-empty>
		</view>
		<view class="" style="height: 150rpx;">

		</view>
		<view class=""
			style="position: fixed;width: 100%;height: 120rpx;display: flex;align-items: center;justify-content: center;bottom: 0;background-color: #FFFFFF;"
			v-if="current==0">
			<view class="" @click="sureNext"
				style="height: 80rpx;width: 680rpx;background-color: #FF7E32;display: flex;align-items: center;justify-content: center;border-radius: 16rpx;color: #FFFFFF;">
				下一步
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				statusList: [{
						id: 0,
						name: '待开发票'
					}, {
						id: 1,
						name: '申请中'
					},
					{
						id: 2,
						name: '已完成'
					},
					{
						id: 3,
						name: '无效'
					}
				],
				typeList: [{
						id: 0,
						name: '洗车发票'
					},
					{
						id: 1,
						name: '停车发票'
					},
					{
						id: 2,
						name: '充电发票'
					}
				],
				type: '',
				typeId: 0,
				params: {
					pageNumber: 1,
					pageSize: 10,
					status: 1,
					type: 1
				},
				bool: true,
				invoiceList: [],
				file: '',
				current: '',
				chargeOrderList: [],
				washOrderList: [],
				washIds: [],
				chargeIds: []
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'ids'])
		},
		async onShow() {
			await this.$onLaunched;

			this.washIds = []
			this.chargeIds = []
			console.log(this.ids, 'fapiao');
			this.current = 0
			if (this.ids && this.ids.type) {
				if (this.ids.type == 0) {

					if (this.ids.status == 1) {
						this.params.status = 2
						this.current = 1
					} else if (this.ids.status == 0) {
						let params = {
							pageNumber: 1,
							pageSize: 10,
							// id:this.userInfo.id
						}
						this.$iBox.http('getCarWashNotInvoiceOrderList', params)({
							method: 'post'
						}).then(res => {
							this.washOrderList = res.data.list
						})
					}
					this.typeId = 2
					this.params.type = 3
				} else {
					if (this.ids.status == 1) {
						this.params.status = 2
						this.current = 1
					} else if (this.ids.status == 0) {
						let params = {
							pageNumber: 1,
							pageSize: 10,
							// id:this.userInfo.id
						}
						this.$iBox.http('getCarWashNotInvoiceOrderList', params)({
							method: 'post'
						}).then(res => {
							this.washOrderList = res.data.list
						})
					}
					this.typeId = 0
					this.params.type = 1
				}
				this.params.pageNumber = 1

				this.bool = true
				this.getInvoiceList()

			} else {
				this.typeId = 0
				this.current = 0
				let params = {
					pageNumber: 1,
					pageSize: 10,
					// id:this.userInfo.id
				}
				this.$iBox.http('getCarWashNotInvoiceOrderList', params)({
					method: 'post'
				}).then(res => {
					this.washOrderList = res.data.list
				})
			}

		},
		methods: {
			chooseType(e) {
				console.log(this.current);
				if (this.current != 0) {
					this.type = e
					this.typeId = e.id
					this.bool = true
					this.params.type = e.id + 1
					this.getInvoiceList()
				} else {
					
					this.type = e
					this.typeId = e.id
					this.bool = true
					if (e.id == 0) {
						uni.showLoading({
							title:'加载中...'
							
						})
						let params = {
							pageNumber: 1,
							pageSize: 10,
							// id:this.userInfo.id
						}
						this.$iBox.http('getCarWashNotInvoiceOrderList', params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.washOrderList = res.data.list
						})
					} else if (e.id == 2) {
						uni.showLoading({
							title:'加载中...'
							
						})
						let params = {
							pageNumber: 1,
							pageSize: 10,
							// id:this.userInfo.id
						}
						this.$iBox.http('wxUserGetNotInvoiceChargingPileOrderList', params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.chargeOrderList = res.data.list
						})
					}
				}

			},
			filterPay(e) {
				let list = [{
					id: 1,
					name: '微信支付'
				}, {
					id: 2,
					name: '会员支付'
				}, {
					id: 3,
					name: '支付宝支付'
				}, {
					id: 4,
					name: '加盟店用户'
				}, {
					id: 5,
					name: 'K码'
				}, {
					id: 6,
					name: '易捷支付'
				}, {
					id: 7,
					name: '系统添加'
				}]
				let type = list.filter(item => {
					return item.id == e
				})[0].name

				return type
			},
			filterOrder(e) {
				let list = [{
					id: 0,
					name: '未支付'
				}, {
					id: 1,
					name: '已支付'
				}, {
					id: 2,
					name: '已完成'
				}, {
					id: 3,
					name: '已过期'
				}, {
					id: 4,
					name: '正在申请'
				}, {
					id: 5,
					name: '已退款'
				}, {
					id: 6,
					name: '申请拒绝'
				}]
				let type = list.filter(item => {
					return item.id == e
				})[0].name

				return type
			},
			tochooseWash(e) {
				console.log(e, this.washIds);
				if (!this.washIds.includes(e.id)) {
					this.washIds.push(e.id)
				}
			},
			toChoosePower(e) {
				console.log(e, this.chargeIds);
				if (!this.chargeIds.includes(e.id)) {
					this.chargeIds.push(e.id)
				}
			},
			sureNext() {

				if (this.typeId == 0) {
					if (this.washOrderList.length == 0) {
						uni.showToast({
							icon: 'none',
							title: '暂无开票订单'
						})
						return
					}

					if (this.washIds.length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请选择开票订单'
						})
						return
					}

				} else if (this.typeId == 2) {
					if (this.chargeOrderList.length == 0) {
						uni.showToast({
							icon: 'none',
							title: '暂无开票订单'
						})
						return
					}

					console.log(this.chargeIds);
					if (this.chargeIds.length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请选择开票订单'
						})
						return
					}

				}

				if (this.typeId == 0) {
					uni.navigateTo({
						url: '/pages/fapiao/invoice?id=' + JSON.stringify(this.washIds) + '&type=' + 1
					})
				} else {
					uni.navigateTo({
						url: '/pages/fapiao/invoice?id=' + JSON.stringify(this.chargeIds) + '&type=' + 3
					})
				}

			},
			click(e) {
				console.log(e, this.typeId, 'id');
				this.params.type = this.typeId + 1
				this.current = e.index
				if (e.index == 0) {

					let params = {
						pageNumber: 1,
						pageSize: 10,
						// id:this.userInfo.id
					}
					if (this.typeId == 0) {
						this.$iBox.http('getCarWashNotInvoiceOrderList', params)({
							method: 'post'
						}).then(res => {
							this.washOrderList = res.data.list
						})
					} else if (this.typeId == 2) {
						this.$iBox.http('wxUserGetNotInvoiceChargingPileOrderList', params)({
							method: 'post'
						}).then(res => {
							this.chargeOrderList = res.data.list
						})
					}


				} else if (e.index == 1) {
					this.params.status = 1
					this.bool = true
					this.getInvoiceList()
				} else if (e.index == 2) {
					this.params.status = 2
					this.bool = true
					this.getInvoiceList()
				} else if (e.index == 3) {
					this.params.status = 3
					this.bool = true
					this.getInvoiceList()
				}

			},

			getInvoiceList() {
				uni.showLoading({
					title:'加载中...'
					
				})
				this.$iBox.http('getUserInvoiceApplyList', this.params)({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.invoiceList = res.data.list
				})
			},
			downLoadFile(e) {
				this.file = e
				wx.showLoading({
					title: '加载中',
				})
				console.log(this.file);
				// const fileExtName = 'pdf';
				// const randfile = new Date().getTime() + fileExtName;
				// const newPath = `${wx.env.USER_DATA_PATH}/${randfile}`;
				wx.downloadFile({
					url: this.file, // 本地服务器上pdf文件的地址
					// filePath: newPath,
					success: (res) => {
						console.log(res, 'dd');
						// 只要服务器有响应数据，就会把响应内容写入文件并进入 success 回调，业务需要自行判断是否下载到了想要的内容
						if (res.statusCode === 200) {

							this.dataFile = res.tempFilePath
							console.log(this.dataFile);
							wx.openDocument({
								filePath: this.dataFile,
								// fileType:'xlsx',
								showMenu: true,
								success: function(res) {

									wx.hideLoading()
								},
								fail: (err) => {
									console.log(err, 'res');
								}
							})
						}
					},
					fail: function(res) {
						console.log(res)
					}
				})

			},
		},
		// // 上拉加载
		onReachBottom() {

			if (this.bool) {
				console.log(this.changeBtnId, 'this.changeBtnId');
				++this.params.pageNumber
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getUserInvoiceApplyList', this.params)({
					method: 'post'
				}).then(res => {

					let new_list = this.invoiceList.concat(res.data.list)
					this.invoiceList = new_list
					if (this.invoiceList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})

			}

		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #F4F6F8;
	}
</style>