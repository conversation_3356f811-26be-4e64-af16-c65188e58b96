<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	
	export default {
		onLaunch: function() {
			// 下载网络字体全局
			console.log('登录0');
			wx.login({
				success: res => {
					if (res.code) {
						console.log('登录');
						let that = this;
						uni.showLoading({
							title:'加载中...'
						})
						this.$iBox.http('login', {code:res.code})({
							method: 'post'
						}).then(res => {
							this.toLogin(res.data)
							
							// 第2，判断是否已经授权，更新店铺
							// 第一次调起授权，成功后拉起微信收货地址页面
							let params ={
								pageNumber: 1,
								pageSize: 10,
								longitude: null,
								latitude: null,
							}
							wx.getLocation({
								success: res => {
									params.latitude = res.latitude
									params.longitude = res.longitude
									that.$iBox
										.http('getChargingPileParkList', params)({
											method: 'post'
										})
										.then(res => {
											uni.hideLoading()
												// that.shop = res.data[0]
												that.pushShopList(res.data.list);
												that.pushShop(res.data.list[0]);
											
											this.$isResolve();
										});
								},
								fail:err=> {
									wx.getSetting({
										success: res2 => {
											if (!res2.authSetting['scope.userLocation']) {
												wx.authorize({
													scope: 'scope.userLocation',
													success() {
														// 第一次调起授权，成功后拉起微信收货地址页面
														wx.getLocation({
															success: res => {
																params.latitude = res.latitude
																params.longitude = res.longitude
																that.$iBox
																	.http('getCarWashShopList', params)({
																		method: 'post'
																	})
																	.then(res => {
																		uni.hideLoading()
																			console.log('location', res);
																			// that.shop = res.data[0]
																			that.pushShopList(res.data.list);
																			that.pushShop(res.data.list[0]);
																		
																		that.$isResolve();
																	});
															}
														});
													},
													fail(){
														wx.showModal({
															title: '提示',
															content: '若点击不授权，将无法正常定位门店',
															cancelText: '不授权',
															cancelColor: '#999',
															confirmText: '授权',
															confirmColor: '#f94218',
															success(res) {
																
																if (res.confirm) {
																	console.log('lalala',res)
																	uni.openSetting({
																		success(res) {
																			that.$isResolve();
																		}
																	});
																} else if (res.cancel) {
																	console.log('用户点击取消');
																	that.$iBox
																		.http('getCarWashShopList', params)({
																			method: 'post'
																		})
																		.then(res => {
																			uni.hideLoading()
																				console.log('location', res);
																				// that.shop = res.data[0]
																				that.pushShopList(res.data.list);
																				that.pushShop(res.data.list[0]);
																			
																			that.$isResolve();
																		});
																	
																}
															}
														});										
													}
												});
											} else {
												// 第一次调起授权，成功后拉起微信收货地址页面
												wx.getLocation({
													success: res => {
														params.latitude = res.latitude
														params.longitude = res.longitude
														that.$iBox
															.http('getChargingPileParkList', params)({
																method: 'post'
															})
															.then(res => {
																
																	console.log('location', res);
																	// that.shop = res.data[0]
																	that.pushShopList(res.data.list);
																	that.pushShop(res.data.list[0]);
																
																that.$isResolve();
															});
													}
												});
											}
										}
									});
								}
							});
							
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					}
				},
				fail:err=>{
					console.log(err);
				}
			})
		},
		methods:{
			...mapActions('login',['toLogin','pushShopList', 'pushShop'])
		},
		onShow: function() {
			const updateManager = wx.getUpdateManager()
			
			updateManager.onUpdateReady( () => {
			  wx.showModal({
			    title: '更新提示',
			    content: '新版本已经准备好，是否重启应用？',
			    success:(res)=> {
			      if (res.confirm) {
			        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
			        updateManager.applyUpdate()
			      }
			    }
			  })
			})
			
			updateManager.onUpdateFailed(function () {
			  // 新版本下载失败
			  wx.showModal({
			    title: '已经有新版本',
			    content: '新版本已经准备好，请删除当前小程序重新进入！'
			  })
			})
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
</style>
