// Authentication mixin for selfWash pages
export default {
  data() {
    return {
      isAuthenticating: false
    }
  },
  
  methods: {
    // Unified authentication check
    async ensureAuthenticated() {
      if (!this.userInfo.phone) {
        this.isAuthenticating = true;
        this.hackReset = true;
        this.if_login = true;
        return false;
      }
      return true;
    },
    
    // Handle successful authentication
    onAuthSuccess() {
      this.isAuthenticating = false;
      this.hackReset = false;
      this.if_login = false;
      this.refreshUserData();
    },
    
    // Refresh user-related data after authentication
    async refreshUserData() {
      try {
        await Promise.all([
          this.loadUserBalance(),
          this.loadUserWashCards()
        ]);
      } catch (error) {
        console.error('Failed to refresh user data:', error);
      }
    }
  }
}